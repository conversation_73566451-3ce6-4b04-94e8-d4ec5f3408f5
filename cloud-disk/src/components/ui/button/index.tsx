import React from "react";
import classNames from "classnames";

// Button 组件的属性类型
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * 按钮变体样式
   */
  variant?:
    | "primary"
    | "secondary"
    | "outline"
    | "ghost"
    | "danger"
    | "success"
    | "warning";

  /**
   * 按钮大小
   */
  size?: "xs" | "sm" | "md" | "lg" | "xl";

  /**
   * 是否为加载状态
   */
  loading?: boolean;

  /**
   * 是否为圆角按钮
   */
  rounded?: boolean;

  /**
   * 是否为块级按钮（占满宽度）
   */
  block?: boolean;

  /**
   * 图标（可选）
   */
  icon?: React.ReactNode;

  /**
   * 图标位置
   */
  iconPosition?: "left" | "right";

  /**
   * 子元素
   */
  children?: React.ReactNode;
}

/**
 * 通用 Button 组件
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      loading = false,
      rounded = false,
      block = false,
      icon,
      iconPosition = "left",
      disabled,
      className,
      children,
      ...props
    },
    ref
  ) => {
    // 基础样式
    const baseClasses = [
      "inline-flex",
      "items-center",
      "justify-center",
      "font-medium",
      "transition-all",
      "duration-200",
      "focus:outline-none",
      "focus:ring-2",
      "focus:ring-offset-2",
      "focus:ring-offset-white",
      "dark:focus:ring-offset-gray-900",
      "disabled:opacity-50",
      "disabled:cursor-not-allowed",
    ];

    // 变体样式
    const variantClasses = {
      primary: [
        "bg-blue-500",
        "hover:bg-blue-600",
        "active:bg-blue-700",
        "text-white",
        "border",
        "border-blue-500",
        "hover:border-blue-600",
        "focus:ring-blue-500",
        "dark:bg-blue-600",
        "dark:hover:bg-blue-700",
        "dark:border-blue-600",
      ],
      secondary: [
        "bg-gray-500",
        "hover:bg-gray-600",
        "active:bg-gray-700",
        "text-white",
        "border",
        "border-gray-500",
        "hover:border-gray-600",
        "focus:ring-gray-500",
        "dark:bg-gray-600",
        "dark:hover:bg-gray-700",
        "dark:border-gray-600",
      ],
      outline: [
        "bg-transparent",
        "hover:bg-gray-50",
        "active:bg-gray-100",
        "text-gray-700",
        "border",
        "border-gray-300",
        "hover:border-gray-400",
        "focus:ring-gray-500",
        "dark:text-gray-300",
        "dark:border-gray-600",
        "dark:hover:bg-gray-800",
        "dark:hover:border-gray-500",
      ],
      ghost: [
        "bg-transparent",
        "hover:bg-gray-100",
        "active:bg-gray-200",
        "text-gray-700",
        "border-transparent",
        "focus:ring-gray-500",
        "dark:text-gray-300",
        "dark:hover:bg-gray-800",
      ],
      danger: [
        "bg-red-500",
        "hover:bg-red-600",
        "active:bg-red-700",
        "text-white",
        "border",
        "border-red-500",
        "hover:border-red-600",
        "focus:ring-red-500",
        "dark:bg-red-600",
        "dark:hover:bg-red-700",
        "dark:border-red-600",
      ],
      success: [
        "bg-green-500",
        "hover:bg-green-600",
        "active:bg-green-700",
        "text-white",
        "border",
        "border-green-500",
        "hover:border-green-600",
        "focus:ring-green-500",
        "dark:bg-green-600",
        "dark:hover:bg-green-700",
        "dark:border-green-600",
      ],
      warning: [
        "bg-yellow-500",
        "hover:bg-yellow-600",
        "active:bg-yellow-700",
        "text-white",
        "border",
        "border-yellow-500",
        "hover:border-yellow-600",
        "focus:ring-yellow-500",
        "dark:bg-yellow-600",
        "dark:hover:bg-yellow-700",
        "dark:border-yellow-600",
      ],
    };

    // 尺寸样式
    const sizeClasses = {
      xs: ["text-xs", "px-2", "py-1", "gap-1"],
      sm: ["text-sm", "px-3", "py-1.5", "gap-1.5"],
      md: ["text-sm", "px-4", "py-2", "gap-2"],
      lg: ["text-base", "px-5", "py-2.5", "gap-2.5"],
      xl: ["text-lg", "px-6", "py-3", "gap-3"],
    };

    // 圆角样式
    const roundedClasses = rounded
      ? "rounded-full"
      : {
          xs: "rounded",
          sm: "rounded-md",
          md: "rounded-md",
          lg: "rounded-lg",
          xl: "rounded-lg",
        }[size];

    // 块级样式
    const blockClasses = block ? "w-full" : "";

    // 组合所有样式
    const buttonClasses = classNames(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      roundedClasses,
      blockClasses,
      className
    );

    // 加载图标
    const loadingIcon = (
      <svg
        className={classNames("animate-spin", {
          "w-3 h-3": size === "xs",
          "w-4 h-4": size === "sm" || size === "md",
          "w-5 h-5": size === "lg",
          "w-6 h-6": size === "xl",
        })}
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    // 渲染图标
    const renderIcon = () => {
      if (loading) return loadingIcon;
      if (!icon) return null;
      return icon;
    };

    // 渲染内容
    const renderContent = () => {
      const iconElement = renderIcon();

      if (!children && iconElement) {
        // 只有图标，无文本
        return iconElement;
      }

      if (!iconElement) {
        // 只有文本，无图标
        return children;
      }

      // 图标 + 文本
      if (iconPosition === "right") {
        return (
          <>
            {children}
            {iconElement}
          </>
        );
      }

      return (
        <>
          {iconElement}
          {children}
        </>
      );
    };

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        {...props}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
