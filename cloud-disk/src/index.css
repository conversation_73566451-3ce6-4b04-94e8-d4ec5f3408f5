@import "tailwindcss";

/* 自定义暗色模式变体 - Tailwind CSS 4.x */
@custom-variant dark (&:where(.dark, .dark *));

/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 浅色主题 */
.light {
  color-scheme: light;
}

.light body {
  background-color: #ffffff;
  color: #1f2937;
}

/* 深色主题 */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #111827;
  color: #f9fafb;
}
