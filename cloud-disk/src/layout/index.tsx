import { Outlet, Link, useLocation } from "react-router-dom";
import { useThemeEffect } from "../hooks/useThemeEffect";
import ThemeToggle from "../components/ThemeToggle";

const Layout = () => {
  const location = useLocation();
  useThemeEffect();

  const navigation = [
    { name: "首页", href: "/", icon: "🏠" },
    { name: "文件管理", href: "/files", icon: "📁" },
  ];

  const isActive = (href: string) => {
    if (href === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* 顶部导航栏 */}
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            {/* Logo 区域 */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg font-bold">☁️</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                云盘系统
              </span>
            </Link>

            {/* 导航菜单 */}
            <div className="hidden md:flex items-center space-x-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200
                    ${
                      isActive(item.href)
                        ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                    }
                  `}
                >
                  <span className="mr-1">{item.icon}</span>
                  {item.name}
                </Link>
              ))}
            </div>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              <ThemeToggle />
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main>
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
