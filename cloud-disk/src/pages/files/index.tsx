import { Link } from "react-router-dom";
import Button from "../../components/ui/button";

const FilesPage = () => {
  // 模拟文件数据
  const files = [
    {
      id: 1,
      name: "项目文档.pdf",
      size: "2.3 MB",
      type: "pdf",
      modified: "2025-01-15",
    },
    {
      id: 2,
      name: "设计稿.sketch",
      size: "15.6 MB",
      type: "design",
      modified: "2025-01-14",
    },
    {
      id: 3,
      name: "产品演示.mp4",
      size: "128.5 MB",
      type: "video",
      modified: "2025-01-13",
    },
    {
      id: 4,
      name: "数据报告.xlsx",
      size: "4.7 MB",
      type: "excel",
      modified: "2025-01-12",
    },
    {
      id: 5,
      name: "代码备份.zip",
      size: "45.2 MB",
      type: "archive",
      modified: "2025-01-11",
    },
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return "📄";
      case "design":
        return "🎨";
      case "video":
        return "🎬";
      case "excel":
        return "📊";
      case "archive":
        return "📦";
      default:
        return "📁";
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case "pdf":
        return "text-red-600 dark:text-red-400";
      case "design":
        return "text-purple-600 dark:text-purple-400";
      case "video":
        return "text-blue-600 dark:text-blue-400";
      case "excel":
        return "text-green-600 dark:text-green-400";
      case "archive":
        return "text-orange-600 dark:text-orange-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const handleUpload = () => {
    alert("上传功能开发中...");
  };

  const handleCreateFolder = () => {
    alert("新建文件夹功能开发中...");
  };

  const handleSearch = () => {
    alert("搜索功能开发中...");
  };

  const handleDownload = (fileName: string) => {
    alert(`下载 ${fileName} 功能开发中...`);
  };

  const handleDelete = (fileName: string) => {
    const confirmed = confirm(`确定要删除 ${fileName} 吗？`);
    if (confirmed) {
      alert(`删除 ${fileName} 功能开发中...`);
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <div className="container mx-auto px-4 py-8">
        {/* 头部导航 */}
        <nav className="mb-6">
          <Link
            to="/"
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            ← 返回首页
          </Link>
        </nav>

        {/* 页面标题 */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            文件管理
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            管理您的所有文件和文档
          </p>
        </header>

        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div className="flex flex-wrap gap-2">
            <Button
              variant="primary"
              icon={<span>📤</span>}
              onClick={handleUpload}
            >
              上传文件
            </Button>
            <Button
              variant="success"
              icon={<span>📁</span>}
              onClick={handleCreateFolder}
            >
              新建文件夹
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="搜索文件..."
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button
              variant="ghost"
              size="sm"
              icon={<span>🔍</span>}
              onClick={handleSearch}
            />
          </div>
        </div>

        {/* 文件列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* 表头 */}
          <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3 border-b border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700 dark:text-gray-300">
              <div className="col-span-6">名称</div>
              <div className="col-span-2">大小</div>
              <div className="col-span-2">修改时间</div>
              <div className="col-span-2">操作</div>
            </div>
          </div>

          {/* 文件列表 */}
          <div className="divide-y divide-gray-200 dark:divide-gray-600">
            {files.map((file) => (
              <div
                key={file.id}
                className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="grid grid-cols-12 gap-4 items-center">
                  <div className="col-span-6 flex items-center space-x-3">
                    <span className="text-2xl">{getFileIcon(file.type)}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {file.name}
                      </p>
                      <p className={`text-xs ${getFileTypeColor(file.type)}`}>
                        {file.type.toUpperCase()}
                      </p>
                    </div>
                  </div>

                  <div className="col-span-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {file.size}
                    </span>
                  </div>

                  <div className="col-span-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {file.modified}
                    </span>
                  </div>

                  <div className="col-span-2 flex space-x-2">
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => handleDownload(file.name)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-1"
                    >
                      下载
                    </Button>
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => handleDelete(file.name)}
                      className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-1"
                    >
                      删除
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 存储使用情况 */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            存储使用情况
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">已使用</span>
              <span className="text-gray-900 dark:text-white font-medium">
                256.3 GB / 1 TB
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-500 dark:bg-blue-600 h-2 rounded-full"
                style={{ width: "25.6%" }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              还剩 743.7 GB 可用空间
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilesPage;
