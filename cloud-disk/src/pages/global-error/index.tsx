import { Link, useRouteError } from "react-router-dom";
import Button from "../../components/ui/button";

interface RouteError {
  statusText?: string;
  message?: string;
  status?: number;
}

const ErrorPage = () => {
  const error = useRouteError() as RouteError;

  const getErrorInfo = () => {
    if (error?.status === 404) {
      return {
        title: "页面未找到",
        message: "抱歉，您访问的页面不存在。",
        emoji: "🔍",
        suggestion: "请检查网址是否正确，或返回首页重新开始。",
      };
    }

    return {
      title: "出现错误",
      message: error?.statusText || error?.message || "发生了未知错误",
      emoji: "⚠️",
      suggestion: "请稍后重试，或联系技术支持。",
    };
  };

  const errorInfo = getErrorInfo();

  const handleGoBack = () => {
    window.history.back();
  };

  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="text-center">
          {/* 错误图标 */}
          <div className="text-6xl mb-6">{errorInfo.emoji}</div>

          {/* 错误标题 */}
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {errorInfo.title}
          </h1>

          {/* 错误信息 */}
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {errorInfo.message}
          </p>

          {/* 建议 */}
          <p className="text-sm text-gray-500 dark:text-gray-500 mb-8">
            {errorInfo.suggestion}
          </p>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Link to="/">
              <Button variant="primary" block icon={<span>🏠</span>}>
                返回首页
              </Button>
            </Link>

            <Button
              variant="secondary"
              block
              icon={<span>←</span>}
              onClick={handleGoBack}
            >
              返回上一页
            </Button>

            <Button
              variant="outline"
              block
              icon={<span>🔄</span>}
              onClick={handleReload}
            >
              重新加载
            </Button>
          </div>

          {/* 错误详情（开发环境） */}
          {process.env.NODE_ENV === "development" && error && (
            <details className="mt-8 text-left">
              <summary className="text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                查看错误详情
              </summary>
              <pre className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-md text-xs text-gray-800 dark:text-gray-200 overflow-auto">
                {JSON.stringify(error, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
