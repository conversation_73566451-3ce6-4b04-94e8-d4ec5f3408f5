import { Link } from "react-router-dom";
import Button from "../../components/ui/button";

const HomePage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 欢迎区域 */}
      <section className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          欢迎使用云盘系统
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
          一个现代化的文件管理和协作平台
        </p>
      </section>

      {/* 主要内容区域 */}
      <main className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 功能卡片 1 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-500 dark:bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">📁</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              文件管理
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            上传、下载、预览和管理您的文件，支持多种文件格式。
          </p>
          <Link to="/files">
            <Button variant="primary" block icon={<span>📁</span>}>
              开始使用
            </Button>
          </Link>
        </div>

        {/* 功能卡片 2 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-green-500 dark:bg-green-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">🔄</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              同步备份
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            自动同步您的文件到云端，确保数据安全不丢失。
          </p>
          <Button
            variant="success"
            block
            icon={<span>🔄</span>}
            onClick={() => alert("同步功能开发中...")}
          >
            设置同步
          </Button>
        </div>

        {/* 功能卡片 3 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-purple-500 dark:bg-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">👥</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              团队协作
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            与团队成员共享文件，支持实时协作和权限管理。
          </p>
          <Button
            variant="secondary"
            block
            icon={<span>👥</span>}
            onClick={() => alert("团队协作功能开发中...")}
            className="bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 border-purple-500 hover:border-purple-600"
          >
            邀请成员
          </Button>
        </div>
      </main>

      {/* 统计信息 */}
      <section className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 text-center">
          <div className="text-2xl font-bold text-blue-500 dark:text-blue-400">
            1.2TB
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            总存储空间
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 text-center">
          <div className="text-2xl font-bold text-green-500 dark:text-green-400">
            256
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            文件数量
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 text-center">
          <div className="text-2xl font-bold text-purple-500 dark:text-purple-400">
            8
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            团队成员
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 text-center">
          <div className="text-2xl font-bold text-orange-500 dark:text-orange-400">
            99.9%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">可用性</div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
