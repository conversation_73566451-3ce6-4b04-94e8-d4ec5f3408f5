{"name": "keep-alive-iframe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "remove": "rimraf node_modules"}, "dependencies": {"@iconify/vue": "^5.0.0", "@vueuse/core": "^13.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "rimraf": "^6.0.1", "typescript": "~5.8.3", "unocss": "^66.1.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@9.5.0"}