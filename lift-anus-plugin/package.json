{"name": "lift-anus-plugin", "displayName": "lift-anus-plugin", "description": "", "version": "0.0.1", "engines": {"vscode": "^1.101.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "lift-anus-plugin.helloWorld", "title": "Hello World"}, {"command": "lift-anus-plugin.startPeriodicMessage", "title": "启动定期提醒"}, {"command": "lift-anus-plugin.stopPeriodicMessage", "title": "停止定期提醒"}, {"command": "lift-anus-plugin.showMessageNow", "title": "立即显示提醒消息"}], "configuration": {"title": "Lift Anus Plugin", "properties": {"liftAnusPlugin.messageInterval": {"type": "number", "default": 3600000, "description": "消息显示间隔时间（毫秒），默认为1小时（3600000毫秒）", "minimum": 60000}, "liftAnusPlugin.enablePeriodicMessage": {"type": "boolean", "default": true, "description": "是否启用定期消息显示"}, "liftAnusPlugin.customMessage": {"type": "string", "default": "该休息一下了！记得保护好你的身体健康！", "description": "自定义显示的消息内容"}}}}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.101.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2"}}