import * as vscode from "vscode";

// 全局变量来存储定时器
let messageTimer: NodeJS.Timeout | undefined;

export function activate(context: vscode.ExtensionContext) {
  // 注册启动定期消息命令
  const startPeriodicDisposable = vscode.commands.registerCommand(
    "lift-anus-plugin.startPeriodicMessage",
    () => {
      const config = vscode.workspace.getConfiguration("liftAnusPlugin");
      config
        .update(
          "enablePeriodicMessage",
          true,
          vscode.ConfigurationTarget.Global
        )
        .then(() => {
          stopPeriodicMessage();
          startPeriodicMessage();
          vscode.window.showInformationMessage("定期提醒已启动！");
        });
    }
  );

  // 注册停止定期消息命令
  const stopPeriodicDisposable = vscode.commands.registerCommand(
    "lift-anus-plugin.stopPeriodicMessage",
    () => {
      const config = vscode.workspace.getConfiguration("liftAnusPlugin");
      config
        .update(
          "enablePeriodicMessage",
          false,
          vscode.ConfigurationTarget.Global
        )
        .then(() => {
          stopPeriodicMessage();
          vscode.window.showInformationMessage("定期提醒已停止！");
        });
    }
  );

  // 注册立即显示消息命令
  const showNowDisposable = vscode.commands.registerCommand(
    "lift-anus-plugin.showMessageNow",
    () => {
      const config = vscode.workspace.getConfiguration("liftAnusPlugin");
      const customMessage = config.get<string>(
        "customMessage",
        "该休息一下了！记得保护好你的身体健康！"
      );
      vscode.window.showInformationMessage(customMessage);
    }
  );

  // 启动定期消息功能
  startPeriodicMessage();

  // 监听配置变化
  const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(
    (event) => {
      if (event.affectsConfiguration("liftAnusPlugin")) {
        // 配置发生变化时重新启动定期消息
        stopPeriodicMessage();
        startPeriodicMessage();
      }
    }
  );

  context.subscriptions.push(
    startPeriodicDisposable,
    stopPeriodicDisposable,
    showNowDisposable,
    configChangeDisposable
  );
}

// 启动定期消息功能
function startPeriodicMessage() {
  const config = vscode.workspace.getConfiguration("liftAnusPlugin");
  const isEnabled = config.get<boolean>("enablePeriodicMessage", true);

  if (!isEnabled) {
    console.log("定期消息功能已禁用");
    return;
  }

  const interval = config.get<number>("messageInterval", 3600000); // 默认1小时
  const customMessage = config.get<string>(
    "customMessage",
    "该休息一下了！记得保护好你的身体健康！"
  );

  console.log(
    `启动定期消息，间隔：${interval}毫秒（${interval / 1000 / 60}分钟）`
  );

  messageTimer = setInterval(() => {
    vscode.window
      .showInformationMessage(customMessage, "知道了", "暂停提醒")
      .then((selection) => {
        if (selection === "暂停提醒") {
          // 用户选择暂停提醒，停止定时器
          stopPeriodicMessage();
          vscode.window.showInformationMessage(
            "定期提醒已暂停。你可以在设置中重新启用。"
          );

          // 更新配置以禁用定期消息
          const config = vscode.workspace.getConfiguration("liftAnusPlugin");
          config.update(
            "enablePeriodicMessage",
            false,
            vscode.ConfigurationTarget.Global
          );
        }
      });
  }, interval);
}

// 停止定期消息功能
function stopPeriodicMessage() {
  if (messageTimer) {
    clearInterval(messageTimer);
    messageTimer = undefined;
    console.log("定期消息已停止");
  }
}

export function deactivate() {
  stopPeriodicMessage();
}
