{"name": "oi", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "tailwindcss": "^4.1.11", "vue": "^3.5.17"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.3", "@types/chrome": "^0.1.1", "@types/node": "^24.0.15", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.5", "vite-plugin-zip-pack": "^1.2.4", "vue-tsc": "^3.0.3"}}