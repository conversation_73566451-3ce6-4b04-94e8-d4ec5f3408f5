/*
 * Content Script 样式 - 确保样式隔离
 * 所有样式都限定在 #oi-search-overlay 内部
 */

@import "tailwindcss";
@import "../styles/theme-variables.css";

/* 样式重置和隔离 - 更温和的重置方式 */
#oi-search-overlay {
  /* 重置可能影响布局的属性，但保留Tailwind需要的基础样式 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
  color: inherit !important;
  text-align: left !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  text-shadow: none !important;
  box-shadow: none !important;
  transform: none !important;
  transition: none !important;
  animation: none !important;
}

#oi-search-overlay *,
#oi-search-overlay *::before,
#oi-search-overlay *::after {
  box-sizing: border-box !important;
}

/* Content Script 覆盖层样式 */
#oi-search-overlay {
  z-index: 2147483647 !important;
  display: flex !important;
  align-items: flex-start !important;
  justify-content: center !important;
  padding-top: 10vh !important;
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}

#oi-search-container {
  width: 800px !important;
  max-width: 90vw !important;
  height: 600px !important;
  max-height: 80vh !important;
  background: rgb(var(--color-background)) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-2xl) !important;
  overflow: hidden !important;
  animation: oi-fade-in 0.2s ease-out;
  border: 1px solid rgb(var(--color-border-primary) / 0.8) !important;
}

@keyframes oi-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 确保所有子元素的基础样式 */
#oi-search-overlay * {
  box-sizing: border-box !important;
}

/* 确保Tailwind类能够正确工作 */
#oi-search-overlay .flex {
  display: flex !important;
}

#oi-search-overlay .flex-col {
  flex-direction: column !important;
}

#oi-search-overlay .flex-1 {
  flex: 1 1 0% !important;
}

#oi-search-overlay .items-center {
  align-items: center !important;
}

#oi-search-overlay .justify-center {
  justify-content: center !important;
}

#oi-search-overlay .gap-1 {
  gap: 0.25rem !important;
}

#oi-search-overlay .gap-2 {
  gap: 0.5rem !important;
}

#oi-search-overlay .gap-3 {
  gap: 0.75rem !important;
}

#oi-search-overlay .p-2 {
  padding: 0.5rem !important;
}

#oi-search-overlay .p-3 {
  padding: 0.75rem !important;
}

#oi-search-overlay .p-4 {
  padding: 1rem !important;
}

#oi-search-overlay .px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

#oi-search-overlay .px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

#oi-search-overlay .py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

#oi-search-overlay .py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

#oi-search-overlay .text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

#oi-search-overlay .text-base {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

#oi-search-overlay .font-medium {
  font-weight: 500 !important;
}

#oi-search-overlay .text-gray-900 {
  color: rgb(var(--color-text-primary)) !important;
}

#oi-search-overlay .text-gray-500 {
  color: rgb(var(--color-text-tertiary)) !important;
}

#oi-search-overlay .bg-white {
  background-color: rgb(var(--color-background)) !important;
}

#oi-search-overlay .bg-gray-50 {
  background-color: rgb(var(--color-background-secondary)) !important;
}

#oi-search-overlay .border {
  border-width: 1px !important;
}

#oi-search-overlay .border-gray-200 {
  border-color: rgb(var(--color-border-primary)) !important;
}

#oi-search-overlay .rounded-lg {
  border-radius: 0.5rem !important;
}

#oi-search-overlay .rounded-xl {
  border-radius: 0.75rem !important;
}

/* 更多常用的Tailwind类 */
#oi-search-overlay .w-5 {
  width: 1.25rem !important;
}

#oi-search-overlay .h-5 {
  height: 1.25rem !important;
}

#oi-search-overlay .w-20 {
  width: 5rem !important;
}

#oi-search-overlay .w-full {
  width: 100% !important;
}

#oi-search-overlay .h-full {
  height: 100% !important;
}

#oi-search-overlay .flex-shrink-0 {
  flex-shrink: 0 !important;
}

#oi-search-overlay .min-w-0 {
  min-width: 0px !important;
}

#oi-search-overlay .overflow-hidden {
  overflow: hidden !important;
}

#oi-search-overlay .overflow-y-auto {
  overflow-y: auto !important;
}

#oi-search-overlay .mr-3 {
  margin-right: 0.75rem !important;
}

#oi-search-overlay .mb-0\.5 {
  margin-bottom: 0.125rem !important;
}

#oi-search-overlay .text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

#oi-search-overlay .whitespace-nowrap {
  white-space: nowrap !important;
}

#oi-search-overlay .text-ellipsis {
  text-overflow: ellipsis !important;
}

#oi-search-overlay .text-gray-400 {
  color: rgb(156 163 175) !important;
}

#oi-search-overlay .text-gray-600 {
  color: rgb(75 85 99) !important;
}

#oi-search-overlay .text-gray-700 {
  color: rgb(55 65 81) !important;
}

#oi-search-overlay .bg-blue-50 {
  background-color: rgb(239 246 255) !important;
}

#oi-search-overlay .bg-blue-500 {
  background-color: rgb(59 130 246) !important;
}

#oi-search-overlay .bg-sky-50 {
  background-color: rgb(240 249 255) !important;
}

#oi-search-overlay .bg-sky-100 {
  background-color: rgb(224 242 254) !important;
}

#oi-search-overlay .bg-amber-100 {
  background-color: rgb(254 243 199) !important;
}

#oi-search-overlay .bg-blue-100 {
  background-color: rgb(219 234 254) !important;
}

#oi-search-overlay .bg-gray-100 {
  background-color: rgb(243 244 246) !important;
}

#oi-search-overlay .border-blue-500 {
  border-color: rgb(59 130 246) !important;
}

#oi-search-overlay .border-sky-400 {
  border-color: rgb(56 189 248) !important;
}

#oi-search-overlay .border-transparent {
  border-color: transparent !important;
}

#oi-search-overlay .text-blue-800 {
  color: rgb(30 64 175) !important;
}

#oi-search-overlay .text-sky-800 {
  color: rgb(7 89 133) !important;
}

#oi-search-overlay .text-amber-800 {
  color: rgb(146 64 14) !important;
}

#oi-search-overlay .opacity-50 {
  opacity: 0.5 !important;
}

#oi-search-overlay .cursor-not-allowed {
  cursor: not-allowed !important;
}

#oi-search-overlay .cursor-pointer {
  cursor: pointer !important;
}

#oi-search-overlay .transition-all {
  transition-property: all !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 150ms !important;
}

#oi-search-overlay .duration-150 {
  transition-duration: 150ms !important;
}

#oi-search-overlay .my-0\.5 {
  margin-top: 0.125rem !important;
  margin-bottom: 0.125rem !important;
}

#oi-search-overlay .px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

#oi-search-overlay .py-0\.5 {
  padding-top: 0.125rem !important;
  padding-bottom: 0.125rem !important;
}

#oi-search-overlay .rounded {
  border-radius: 0.25rem !important;
}

#oi-search-overlay .uppercase {
  text-transform: uppercase !important;
}

#oi-search-overlay .tracking-wider {
  letter-spacing: 0.05em !important;
}

#oi-search-overlay .line-through {
  text-decoration-line: line-through !important;
}

#oi-search-overlay .ml-2 {
  margin-left: 0.5rem !important;
}

#oi-search-overlay .text-red-500 {
  color: rgb(239 68 68) !important;
}

/* 搜索高亮样式 */
#oi-search-overlay .oi-highlight {
  background-color: rgb(var(--color-highlight-bg)) !important;
  color: rgb(var(--color-highlight-text)) !important;
  padding: 1px 2px !important;
  border-radius: 2px !important;
  font-weight: 600 !important;
}



/* 滚动条样式 */
#oi-search-overlay .scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

#oi-search-overlay .scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

#oi-search-overlay .scrollbar-thin::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

#oi-search-overlay .scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb) !important;
  border-radius: 4px !important;
  border: 2px solid transparent !important;
  background-clip: content-box !important;
  transition: background-color 0.2s ease !important;
}

#oi-search-overlay .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover) !important;
}

#oi-search-overlay .scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active) !important;
}

/* 简化右键菜单样式 - 专为content script设计 */
#oi-search-overlay .oi-simple-context-menu {
  position: fixed !important;
  z-index: 2147483647 !important;
  background: rgb(var(--color-background)) !important;
  border: 1px solid rgb(var(--color-border-primary)) !important;
  border-radius: 0.75rem !important;
  box-shadow: var(--shadow-xl) !important;
  padding: 0.5rem 0 !important;
  min-width: 140px !important;
  backdrop-filter: blur(4px) !important;
}

#oi-search-overlay .oi-simple-context-menu-item {
  display: flex !important;
  align-items: center !important;
  padding: 0.625rem 1rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  color: rgb(var(--color-text-secondary)) !important;
  cursor: pointer !important;
  transition: background-color 200ms !important;
}

#oi-search-overlay .oi-simple-context-menu-item:hover {
  background-color: rgb(var(--color-background-secondary)) !important;
}

#oi-search-overlay .oi-simple-context-menu-icon {
  margin-right: 0.75rem !important;
  flex-shrink: 0 !important;
  width: 1rem !important;
  height: 1rem !important;
}

#oi-search-overlay .oi-simple-context-menu-icon.navigate {
  color: rgb(37 99 235) !important;
}

#oi-search-overlay .oi-simple-context-menu-icon.close,
#oi-search-overlay .oi-simple-context-menu-icon.remove {
  color: rgb(220 38 38) !important;
}

/* 右键菜单完整样式支持 */

/* 保留原有的Tailwind类支持 */
#oi-search-overlay .fixed {
  position: fixed !important;
}

#oi-search-overlay .z-50 {
  z-index: 50 !important;
}

#oi-search-overlay .min-w-\[140px\] {
  min-width: 140px !important;
}

#oi-search-overlay .backdrop-blur-sm {
  backdrop-filter: blur(4px) !important;
}

#oi-search-overlay .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

#oi-search-overlay .hover\:bg-gray-50:hover {
  background-color: rgb(249 250 251) !important;
}

#oi-search-overlay .group:hover .group-hover\:text-blue-600 {
  color: rgb(37 99 235) !important;
}

#oi-search-overlay .text-blue-600 {
  color: rgb(37 99 235) !important;
}

#oi-search-overlay .text-red-600 {
  color: rgb(220 38 38) !important;
}

#oi-search-overlay .px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

#oi-search-overlay .py-2\.5 {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

#oi-search-overlay .mr-3 {
  margin-right: 0.75rem !important;
}

#oi-search-overlay .w-4 {
  width: 1rem !important;
}

#oi-search-overlay .h-4 {
  height: 1rem !important;
}

#oi-search-overlay .duration-200 {
  transition-duration: 200ms !important;
}

#oi-search-overlay .group {
  position: relative !important;
}

/* 更多必要的样式类 */
#oi-search-overlay .flex-shrink-0 {
  flex-shrink: 0 !important;
}

#oi-search-overlay .font-medium {
  font-weight: 500 !important;
}

#oi-search-overlay .text-gray-700 {
  color: rgb(55 65 81) !important;
}

#oi-search-overlay .border-2 {
  border-width: 2px !important;
}

#oi-search-overlay .border-blue-300 {
  border-color: rgb(147 197 253) !important;
}

#oi-search-overlay .text-blue-700 {
  color: rgb(29 78 216) !important;
}

#oi-search-overlay .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

#oi-search-overlay .hover\:bg-blue-50:hover {
  background-color: rgb(239 246 255) !important;
}

#oi-search-overlay .hover\:text-blue-600:hover {
  color: rgb(37 99 235) !important;
}

/* 键盘提示样式 */
#oi-search-overlay .border-t {
  border-top-width: 1px !important;
}

#oi-search-overlay .gap-4 {
  gap: 1rem !important;
}

#oi-search-overlay .gap-1 {
  gap: 0.25rem !important;
}

#oi-search-overlay .py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

#oi-search-overlay .px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

#oi-search-overlay .text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

#oi-search-overlay .text-gray-500 {
  color: rgb(107 114 128) !important;
}

#oi-search-overlay kbd {
  display: inline-block !important;
  padding: 0.125rem 0.375rem !important;
  background-color: white !important;
  border: 1px solid rgb(209 213 219) !important;
  border-radius: 0.25rem !important;
  font-size: 0.75rem !important;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
  color: rgb(55 65 81) !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

#oi-search-overlay .px-1\.5 {
  padding-left: 0.375rem !important;
  padding-right: 0.375rem !important;
}

#oi-search-overlay .py-0\.5 {
  padding-top: 0.125rem !important;
  padding-bottom: 0.125rem !important;
}

#oi-search-overlay .font-mono {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
}
