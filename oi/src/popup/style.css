@import "tailwindcss";
@import "../styles/theme-variables.css";

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  width: 480px;
  height: 600px;
  overflow: hidden;
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-text-primary));
}

#app {
  width: 100%;
  height: 100%;
}

/* 搜索高亮样式 */
.oi-highlight {
  background-color: rgb(var(--color-highlight-bg));
  color: rgb(var(--color-highlight-text));
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

/* 优化滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

.scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active);
}
