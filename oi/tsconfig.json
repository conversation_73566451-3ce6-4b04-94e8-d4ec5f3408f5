{"compilerOptions": {"target": "es2020", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "chrome"], "allowImportingTsExtensions": true, "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules"]}