{"root": ["./manifest.config.ts", "./vite.config.ts", "./src/background/main.ts", "./src/components/categorysidebar.vue", "./src/components/contextmenu.vue", "./src/components/menuicon.vue", "./src/components/resultslist.vue", "./src/components/search.vue", "./src/components/themetoggle.vue", "./src/composables/usecategories.ts", "./src/composables/useresultslist.ts", "./src/composables/usesearch.ts", "./src/composables/usetheme.ts", "./src/content/contentsearch.vue", "./src/content/content-app.ts", "./src/content/main.ts", "./src/popup/main.ts", "./src/popup/main.vue", "./src/services/basedataservice.ts", "./src/services/dataservice.ts", "./src/utils/chrome-api.ts", "./src/utils/content-data-service.ts", "./src/utils/highlight.ts"], "version": "5.8.3"}