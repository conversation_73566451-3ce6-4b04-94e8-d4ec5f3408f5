{
  // This tsconfig is for development. Allowing *.story.vue, and __test__/*.vue to have types support
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["./src/*"]
    },
    "resolveJsonModule": true,
    "types": ["vitepress/client", "vue"],
    "strict": true,
    "declaration": false,
    "outDir": "dist",
    "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "/**/*.vue",
    "components/**/*.vue",
    ".vitepress/**/*.vue",
    "/**/*.ts",
    ".vitepress/**/*.ts",
    ".vitepress/**/*.vue",
    "src/lib/**/*"
  ],
  "exclude": ["node_modules"]
}
