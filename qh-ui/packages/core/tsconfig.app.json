{
  // This tsconfig is for development. Allowing *.story.vue, and __test__/*.vue to have types support
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": ["node"],
    "strict": true,
    "declaration": false,
    "outDir": "dist",
    "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}
