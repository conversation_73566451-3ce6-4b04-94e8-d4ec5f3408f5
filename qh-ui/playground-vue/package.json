{"name": "playground-vue", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "qh-ui": "link:../packages/core", "tailwindcss": "^4.1.11", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}