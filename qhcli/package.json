{"name": "@qinghuanaa/qhcli", "version": "1.2.1", "description": "A CLI tool for quickly creating projects", "main": "dist/index.js", "type": "commonjs", "bin": {"qhcli": "./dist/index.js"}, "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "release": "pnpm build && bumpp --commit --push --tag && npm publish", "release:patch": "bumpp --commit --push --tag --patch", "release:minor": "bumpp --commit --push --tag --minor", "release:major": "bumpp --commit --push --tag --major"}, "keywords": ["cli", "scaffold", "template"], "author": "", "license": "ISC", "dependencies": {"commander": "^11.1.0", "inquirer": "^7.3.3", "simple-git": "^3.27.0"}, "devDependencies": {"@types/inquirer": "^7.3.3", "@types/node": "^20.11.19", "bumpp": "^10.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}