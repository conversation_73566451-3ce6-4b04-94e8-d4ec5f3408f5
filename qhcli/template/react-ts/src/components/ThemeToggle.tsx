import { useGlobalStore, type Theme } from "../store";
import Button from "./ui/button";

const ThemeToggle = () => {
  const theme = useGlobalStore((state) => state.theme);
  const setTheme = useGlobalStore((state) => state.setTheme);

  const themes: { value: Theme; label: string; icon: string }[] = [
    { value: "light", label: "浅色", icon: "☀️" },
    { value: "dark", label: "深色", icon: "🌙" },
    { value: "system", label: "跟随系统", icon: "💻" },
  ];

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
        主题:
      </span>
      <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 gap-1">
        {themes.map((themeOption) => (
          <Button
            key={themeOption.value}
            variant={theme === themeOption.value ? "outline" : "ghost"}
            size="xs"
            icon={<span>{themeOption.icon}</span>}
            onClick={() => setTheme(themeOption.value)}
            title={themeOption.label}
            className={`
              border-0 
              ${
                theme === themeOption.value
                  ? "!bg-white dark:!bg-gray-700 !text-gray-900 dark:!text-gray-200 shadow-sm"
                  : "!bg-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:!bg-gray-50 dark:hover:!bg-gray-700"
              }
            `}
          >
            {themeOption.label}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default ThemeToggle;
