import { Outlet, Link, useLocation } from "react-router-dom";
import { useThemeEffect } from "../hooks/useThemeEffect";
import ThemeToggle from "../components/ThemeToggle";

const Layout = () => {
  const location = useLocation();
  useThemeEffect();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* 顶部导航栏 */}
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <ThemeToggle />
      </nav>

      {/* 主要内容区域 */}
      <main>
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
