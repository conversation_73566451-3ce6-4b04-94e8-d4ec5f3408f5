import { createBrowserRouter } from "react-router-dom";
import Layout from "../layout";
import HomePage from "../pages/home";
import ErrorPage from "../pages/global-error";

// 路由配置
export const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
    ],
  },
  {
    path: "*",
    element: <ErrorPage />,
  },
]);
