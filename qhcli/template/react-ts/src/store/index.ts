import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// 主题类型
export type Theme = "light" | "dark" | "system";

// 错误类型
export interface AppError {
  message: string;
  code?: string;
  timestamp: number;
}

// Store 状态接口
interface GlobalState {
  // 主题设置
  theme: Theme;

  // 全局加载状态
  isLoading: boolean;
  loadingMessage: string;

  // 错误处理
  error: AppError | null;

  // Actions
  setTheme: (theme: Theme) => void;
  setLoading: (loading: boolean, message?: string) => void;
  setError: (error: string | AppError | null) => void;
  clearError: () => void;
}

// 创建全局 store
export const useGlobalStore = create<GlobalState>()(
  devtools(
    persist(
      (set) => ({
        // 初始状态
        theme: "system",
        isLoading: false,
        loadingMessage: "",
        error: null,

        // 主题相关 actions
        setTheme: (theme) => set({ theme }, false, "setTheme"),

        // 加载状态 actions
        setLoading: (loading, message = "") =>
          set(
            { isLoading: loading, loadingMessage: message },
            false,
            "setLoading"
          ),

        // 错误处理 actions
        setError: (error) => {
          if (error === null) {
            set({ error: null }, false, "clearError");
            return;
          }

          const errorObj: AppError =
            typeof error === "string"
              ? { message: error, timestamp: Date.now() }
              : { ...error, timestamp: error.timestamp || Date.now() };

          set({ error: errorObj }, false, "setError");
        },

        clearError: () => set({ error: null }, false, "clearError"),
      }),
      {
        name: "global-store", // localStorage 键名
        partialize: (state) => ({
          theme: state.theme,
        }), // 只持久化主题设置
      }
    ),
    {
      name: "global-store", // devtools 名称
    }
  )
);

// 导出选择器 hooks 用于优化性能
export const useTheme = () => useGlobalStore((state) => state.theme);
export const useSetTheme = () => useGlobalStore((state) => state.setTheme);

export const useIsLoading = () => useGlobalStore((state) => state.isLoading);
export const useLoadingMessage = () =>
  useGlobalStore((state) => state.loadingMessage);
export const useSetLoading = () => useGlobalStore((state) => state.setLoading);

export const useError = () => useGlobalStore((state) => state.error);
export const useSetError = () => useGlobalStore((state) => state.setError);
export const useClearError = () => useGlobalStore((state) => state.clearError);
