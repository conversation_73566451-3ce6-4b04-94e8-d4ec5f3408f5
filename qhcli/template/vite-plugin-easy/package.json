{"name": "{{projectName}}", "type": "module", "version": "1.0.0", "description": "", "author": "Qinghuan <<EMAIL>>", "license": "MIT", "homepage": "", "repository": {"type": "git", "url": ""}, "keywords": ["vite", "vite-plugin"], "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt.d.mts", "default": "./dist/nuxt.mjs"}, "require": {"types": "./dist/nuxt.d.cts", "default": "./dist/nuxt.cjs"}}, "./*": "./*"}, "scripts": {"build": "rimraf dist && unbuild", "dev": "npm run build --watch", "typecheck": "tsc"}, "engines": {"node": ">=14"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}}, "dependencies": {"fast-glob": "^3.3.2"}, "devDependencies": {"@nuxt/kit": "^3.14.0", "@types/node": "^22.7.5", "rimraf": "^6.0.1", "tsup": "^8.2.4", "typescript": "^5.6.3", "unbuild": "^2.0.0", "vite": "^5.4.4"}}