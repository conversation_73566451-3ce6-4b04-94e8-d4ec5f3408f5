import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  webpack: (config: any) => {
    // 添加对Live2D文件的支持
    config.module.rules.push({
      test: /\.(moc3|model3\.json|cdi3\.json|physics3\.json|motion3\.json)$/,
      type: 'asset/resource',
    });

    return config;
  },
  // 确保静态文件能正确提供
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',
  // 禁用严格模式以避免Live2D的一些兼容性问题
  reactStrictMode: false,
};

export default nextConfig;
