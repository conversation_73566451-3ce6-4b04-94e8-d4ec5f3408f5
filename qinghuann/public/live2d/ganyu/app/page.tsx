'use client'

import { Suspense } from 'react'
import Live2DModelComponent from '../components/Live2DModel'

export default function Home() {
  return (
    <div className="container">
      <h1 className="title">甘雨 Live2D 模型展示</h1>
      <p style={{ color: 'white', textAlign: 'center', marginBottom: '2rem', fontSize: '1.1rem' }}>
        使用 React + Next.js + PixiJS 制作的 Live2D 交互式模型展示
      </p>
      
      <Suspense fallback={<div className="loading">加载中...</div>}>
        <Live2DModelComponent 
          modelPath="/live2d/Ganyu.model3.json"
          width={600}
          height={800}
        />
      </Suspense>

      <div style={{ color: 'white', textAlign: 'center', marginTop: '2rem', opacity: 0.8 }}>
        <p>点击模型进行交互，使用控制按钮播放动作和表情</p>
        <p style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
          技术栈: React + Next.js + PixiJS + Live2D Cubism SDK
        </p>
      </div>
    </div>
  )
} 