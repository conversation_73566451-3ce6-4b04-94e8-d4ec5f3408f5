'use client'

import { useEffect, useRef, useState } from 'react'
import { Application } from 'pixi.js'
import { Live2DModel } from '@pixi/live2d-display'

// 扩展 window 对象类型
declare global {
  interface Window {
    PIXI: any
  }
}

interface Live2DModelProps {
  modelPath: string
  width?: number
  height?: number
}

const Live2DModelComponent: React.FC<Live2DModelProps> = ({ 
  modelPath, 
  width = 600, 
  height = 800 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const appRef = useRef<Application | null>(null)
  const modelRef = useRef<Live2DModel | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initLive2D = async () => {
      try {
        if (!canvasRef.current) return

        // 设置 PIXI 全局引用（Live2D需要）
        if (typeof window !== 'undefined') {
          const PIXI = await import('pixi.js')
          window.PIXI = PIXI
        }

        // 创建 PIXI 应用
        const app = new Application({
          view: canvasRef.current,
          width,
          height,
          backgroundColor: 0x1099bb,
          backgroundAlpha: 0,
        })

        appRef.current = app

        // 加载 Live2D 模型
        const model = await Live2DModel.from(modelPath)
        modelRef.current = model

        // 设置模型大小和位置
        const scale = Math.min(width / model.width, height / model.height) * 0.8
        model.scale.set(scale)
        model.x = width / 2
        model.y = height / 2
        model.anchor.set(0.5, 0.5)

        // 添加到舞台
        app.stage.addChild(model)

        // 添加交互
        model.on('hit', (hitAreas: string[]) => {
          console.log('Hit areas:', hitAreas)
          // 播放随机动作
          if (model.internalModel.motionManager) {
            const motionGroup = 'Idle' // 或其他动作组
            model.motion(motionGroup)
          }
        })

        setIsLoading(false)
      } catch (err) {
        console.error('Failed to load Live2D model:', err)
        setError(`加载模型失败: ${err instanceof Error ? err.message : '未知错误'}`)
        setIsLoading(false)
      }
    }

    initLive2D()

    // 清理函数
    return () => {
      if (appRef.current) {
        appRef.current.destroy()
      }
    }
  }, [modelPath, width, height])

  // 播放动作
  const playMotion = (motionGroup: string = 'Scene1') => {
    if (modelRef.current) {
      modelRef.current.motion(motionGroup)
    }
  }

  // 播放表情
  const playExpression = (expressionName: string) => {
    if (modelRef.current && modelRef.current.internalModel.motionManager) {
      modelRef.current.expression(expressionName)
    }
  }

  // 随机眨眼
  const randomBlink = () => {
    if (modelRef.current) {
      // 触发眨眼动画
      const eyeBlinkParam = modelRef.current.internalModel.coreModel.getParameterValueByName?.('ParamEyeLOpen')
      if (eyeBlinkParam !== undefined) {
        // 这里可以添加眨眼逻辑
        console.log('Blink triggered')
      }
    }
  }

  if (error) {
    return <div className="error">{error}</div>
  }

  return (
    <div className="live2d-container">
      {isLoading && <div className="loading">正在加载甘雨模型...</div>}
      <canvas 
        ref={canvasRef}
        style={{ 
          display: isLoading ? 'none' : 'block',
          borderRadius: '10px'
        }}
      />
      {!isLoading && (
        <div className="controls">
          <button className="control-btn" onClick={() => playMotion('Scene1')}>
            播放动作
          </button>
          <button className="control-btn" onClick={() => randomBlink()}>
            眨眼
          </button>
          <button className="control-btn" onClick={() => playExpression('f01')}>
            表情1
          </button>
          <button className="control-btn" onClick={() => playExpression('f02')}>
            表情2
          </button>
        </div>
      )}
    </div>
  )
}

export default Live2DModelComponent 