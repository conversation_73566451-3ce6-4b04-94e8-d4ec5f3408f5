{"Version": 3, "Meta": {"Duration": 16.65, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 19, "TotalSegmentCount": 347, "TotalPointCount": 980, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, -2, 1.117, -2, 1, 1.211, -2, 1.306, 0, 1.4, 0, 1, 1.472, 0, 1.544, 0, 1.617, 0, 1, 1.744, 0, 1.872, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 1, 2.656, 0, 2.744, 0, 2.833, 0, 1, 3.15, 0, 3.467, -6, 3.783, -6, 1, 4.15, -6, 4.517, 12, 4.883, 12, 1, 5.456, 12, 6.028, 5.086, 6.6, -2, 1, 6.717, -3.445, 6.833, -3.076, 6.95, -4, 1, 7.017, -4.528, 7.083, -6.256, 7.15, -9, 1, 7.222, -11.972, 7.294, -24, 7.367, -24, 1, 7.456, -24, 7.544, -11, 7.633, -11, 1, 7.772, -11, 7.911, -18, 8.05, -18, 1, 8.117, -18, 8.183, -17.612, 8.25, -6, 1, 8.322, 6.58, 8.394, 30, 8.467, 30, 1, 8.578, 30, 8.689, -30, 8.8, -30, 1, 8.983, -30, 9.167, -30.134, 9.35, -18, 1, 9.533, -5.866, 9.717, 30, 9.9, 30, 1, 10.15, 30, 10.4, 30, 10.65, 30, 1, 11.228, 30, 11.806, 0, 12.383, 0, 1, 12.75, 0, 13.117, 0, 13.483, 0, 1, 13.828, 0, 14.172, 0, 14.517, 0, 1, 14.789, 0, 15.061, -8, 15.333, -8, 1, 15.606, -8, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, 18, 1.117, 18, 1, 1.211, 18, 1.306, -1.251, 1.4, -15, 1, 1.472, -25.514, 1.544, -26, 1.617, -26, 1, 1.744, -12.8, 1.872, -15, 2, -15, 0, 2.567, -1, 0, 2.633, -1, 1, 3.017, -28, 3.467, 15, 3.783, 15, 1, 4.017, 15, 4.25, -30, 4.483, -30, 1, 5.189, -24.6, 6.028, 15, 6.6, 15, 1, 6.717, 15, 6.833, 10.885, 6.95, -6, 1, 7.017, -15.648, 7.083, -30, 7.15, -30, 1, 7.222, -30, 7.294, -16.564, 7.367, 1, 1, 7.456, 22.618, 7.544, 30, 7.633, 30, 1, 7.772, 30, 7.911, 21.828, 8.05, 7, 1, 8.117, -0.117, 8.183, -3, 8.25, -3, 1, 8.322, -3, 8.394, -2.455, 8.467, 3, 1, 8.578, 11.392, 8.689, 20, 8.8, 20, 1, 8.983, 20, 9.167, 18.889, 9.35, 18, 1, 9.533, 17.111, 9.717, 17, 9.9, 17, 1, 10.15, 17, 10.4, 20, 10.65, 20, 1, 11.228, 20, 11.806, -27, 12.383, -27, 1, 12.75, -27, 13.117, -24.799, 13.483, -8, 1, 13.828, 7.781, 14.172, 30, 14.517, 30, 1, 14.789, 30, 15.061, 30, 15.333, 30, 1, 15.606, 30, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, -5, 1.117, -5, 1, 1.211, -5, 1.306, 0, 1.4, 0, 1, 1.472, 0, 1.544, 0, 1.617, 0, 1, 1.744, 0, 1.872, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 1, 2.656, 0, 2.744, 0, 2.833, 0, 1, 3.15, 0, 3.467, -4, 3.783, -4, 1, 4.017, -4, 4.25, -4, 4.483, -4, 1, 4.617, -4, 4.75, -4, 4.883, -4, 1, 5.456, -4, 6.028, -4, 6.6, -4, 1, 6.717, -4, 6.833, -6, 6.95, -6, 1, 7.017, -6, 7.083, -6, 7.15, -6, 1, 7.222, -6, 7.294, -4.176, 7.367, 0, 1, 7.456, 5.14, 7.544, 8, 7.633, 8, 1, 7.772, 8, 7.911, -4, 8.05, -4, 1, 8.117, -4, 8.183, 0, 8.25, 0, 1, 8.322, 0, 8.394, -3, 8.467, -3, 1, 8.578, -3, 8.689, 13, 8.8, 13, 1, 8.983, 13, 9.167, -2, 9.35, -2, 1, 9.533, -2, 9.717, -2, 9.9, -2, 1, 10.15, -2, 10.4, -8, 10.65, -8, 1, 11.228, -8, 11.806, -8, 12.383, -8, 1, 12.75, -8, 13.117, -8, 13.483, -8, 1, 13.828, -8, 14.172, 4, 14.517, 4, 1, 14.789, 4, 15.061, 2, 15.333, 1, 1, 15.606, 0, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 2.922, 0, 5.694, 0, 8.467, 0, 1, 8.578, 0, 8.689, -0.6, 8.8, -0.6, 1, 8.983, -0.6, 9.167, -0.6, 9.35, -0.6, 1, 9.533, -0.6, 9.717, 0, 9.9, 0, 1, 10.15, 0, 10.4, 0, 10.65, 0, 1, 11.228, 0, 11.806, 0, 12.383, 0, 1, 13.094, 0, 13.806, 0, 14.517, 0, 1, 14.789, 0, 15.061, 0, 15.333, 0, 1, 15.606, 0, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 2.922, 0, 5.694, 0, 8.467, 0, 1, 8.578, 0, 8.689, 0.2, 8.8, 0.2, 1, 8.983, 0.2, 9.167, 0.2, 9.35, 0.2, 1, 9.533, 0.2, 9.717, 0, 9.9, 0, 1, 10.15, 0, 10.4, 0, 10.65, 0, 1, 11.228, 0, 11.806, -0.6, 12.383, -0.6, 1, 13.094, -0.6, 13.806, -0.6, 14.517, -0.6, 1, 14.789, -0.6, 15.061, 0, 15.333, 0, 1, 15.606, 0, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 2.922, 0, 5.694, 0, 8.467, 0, 1, 8.578, 0, 8.689, -0.6, 8.8, -0.6, 1, 8.983, -0.6, 9.167, -0.6, 9.35, -0.6, 1, 9.533, -0.6, 9.717, 0, 9.9, 0, 1, 10.15, 0, 10.4, 0, 10.65, 0, 1, 11.228, 0, 11.806, 0, 12.383, 0, 1, 13.094, 0, 13.806, 0, 14.517, 0, 1, 14.789, 0, 15.061, 0, 15.333, 0, 1, 15.606, 0, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 2.922, 0, 5.694, 0, 8.467, 0, 1, 8.578, 0, 8.689, 0.2, 8.8, 0.2, 1, 8.983, 0.2, 9.167, 0.2, 9.35, 0.2, 1, 9.533, 0.2, 9.717, 0, 9.9, 0, 1, 10.15, 0, 10.4, 0, 10.65, 0, 1, 11.228, 0, 11.806, -0.6, 12.383, -0.6, 1, 13.094, -0.6, 13.806, -0.6, 14.517, -0.6, 1, 14.789, -0.6, 15.061, 0, 15.333, 0, 1, 15.606, 0, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 1, 0, 0.15, 1, 1, 0.472, 1, 0.794, 0.949, 1.117, 0.7, 1, 1.211, 0.627, 1.306, 0, 1.4, 0, 1, 1.472, 0, 1.544, 0, 1.617, 0, 1, 1.744, 0, 1.872, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 1, 2.656, 0, 2.744, 0, 2.833, 0, 1, 3.15, 0, 3.467, 0.4, 3.783, 0.4, 1, 4.017, 0.4, 4.25, 0, 4.483, 0, 1, 4.617, 0, 4.75, 0, 4.883, 0, 1, 5.456, 0, 6.028, 0.5, 6.6, 0.5, 1, 6.717, 0.5, 6.833, 0, 6.95, 0, 1, 7.017, 0, 7.083, 0, 7.15, 0, 1, 7.222, 0, 7.294, 1, 7.367, 1, 1, 7.456, 1, 7.544, 1, 7.633, 1, 1, 7.772, 1, 7.911, 1, 8.05, 1, 1, 8.117, 1, 8.183, 0, 8.25, 0, 1, 8.322, 0, 8.394, 0.832, 8.467, 0.9, 1, 8.578, 1.005, 8.689, 1, 8.8, 1, 1, 9.167, 1, 9.533, 0, 9.9, 0, 1, 10.15, 0, 10.4, 1, 10.65, 1, 1, 11.228, 1, 11.806, 0.848, 12.383, 0.7, 1, 12.75, 0.606, 13.117, 0.61, 13.483, 0.5, 1, 13.828, 0.397, 14.172, 0, 14.517, 0, 1, 14.789, 0, 15.061, 1, 15.333, 1, 1, 15.606, 1, 15.878, 1, 16.15, 1, 0, 16.65, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 1, 0, 0.15, 1, 1, 0.472, 1, 0.794, 0.949, 1.117, 0.7, 1, 1.211, 0.627, 1.306, 0, 1.4, 0, 1, 1.472, 0, 1.544, 0, 1.617, 0, 1, 1.744, 0, 1.872, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 1, 2.656, 0, 2.744, 0, 2.833, 0, 1, 3.15, 0, 3.467, 0.4, 3.783, 0.4, 1, 4.017, 0.4, 4.25, 0, 4.483, 0, 1, 4.617, 0, 4.75, 0, 4.883, 0, 1, 5.456, 0, 6.028, 0.5, 6.6, 0.5, 1, 6.717, 0.5, 6.833, 0, 6.95, 0, 1, 7.017, 0, 7.083, 0, 7.15, 0, 1, 7.222, 0, 7.294, 1, 7.367, 1, 1, 7.456, 1, 7.544, 1, 7.633, 1, 1, 7.772, 1, 7.911, 1, 8.05, 1, 1, 8.117, 1, 8.183, 0, 8.25, 0, 1, 8.322, 0, 8.394, 0.832, 8.467, 0.9, 1, 8.578, 1.005, 8.689, 1, 8.8, 1, 1, 9.167, 1, 9.533, 0, 9.9, 0, 1, 10.15, 0, 10.4, 1, 10.65, 1, 1, 11.228, 1, 11.806, 0.848, 12.383, 0.7, 1, 12.75, 0.606, 13.117, 0.61, 13.483, 0.5, 1, 13.828, 0.397, 14.172, 0, 14.517, 0, 1, 14.789, 0, 15.061, 1, 15.333, 1, 1, 15.606, 1, 15.878, 1, 16.15, 1, 0, 16.65, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 5.483, 0, 10.817, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 5.483, 0, 10.817, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0.5, 0, 0.15, 0.5, 1, 0.472, 0.5, 0.794, 0.5, 1.117, 0.5, 1, 1.283, 0.5, 1.45, 0.474, 1.617, 0.4, 1, 1.744, 0.343, 1.872, 0.3, 2, 0.3, 1, 2.828, 0.3, 3.656, 0.4, 4.483, 0.4, 1, 4.617, 0.4, 4.75, 0.4, 4.883, 0.4, 1, 5.639, 0.4, 6.394, 0.4, 7.15, 0.4, 1, 7.222, 0.4, 7.294, 0.4, 7.367, 0.4, 1, 7.456, 0.4, 7.544, 0.4, 7.633, 0.4, 1, 7.911, 0.4, 8.189, 0.4, 8.467, 0.4, 1, 8.528, 0.4, 8.589, 0.4, 8.65, 0.4, 1, 8.944, 0.4, 9.239, 0.4, 9.533, 0.4, 1, 10.144, 0.4, 10.756, 0.4, 11.367, 0.4, 1, 11.706, 0.4, 12.044, 0.4, 12.383, 0.4, 1, 12.75, 0.4, 13.117, 0.4, 13.483, 0.4, 1, 13.828, 0.4, 14.172, 0.399, 14.517, 0.399, 1, 14.789, 0.399, 15.061, 0.399, 15.333, 0.4, 1, 15.606, 0.401, 15.878, 0.5, 16.15, 0.5, 0, 16.65, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, 0, 1.117, 0, 1, 1.283, 0, 1.45, 0.3, 1.617, 0.3, 1, 1.744, 0.3, 1.872, 0, 2, 0, 1, 2.828, 0, 3.656, 0.3, 4.483, 0.3, 1, 4.617, 0.3, 4.75, 0, 4.883, 0, 1, 5.639, 0, 6.394, 0, 7.15, 0, 1, 7.222, 0, 7.294, 0.3, 7.367, 0.3, 1, 7.456, 0.3, 7.544, 0, 7.633, 0, 1, 7.911, 0, 8.189, 0, 8.467, 0, 1, 8.528, 0, 8.589, 0.3, 8.65, 0.3, 1, 8.944, 0.3, 9.239, 0, 9.533, 0, 1, 10.144, 0, 10.756, 0, 11.367, 0, 1, 11.706, 0, 12.044, 0.2, 12.383, 0.2, 1, 12.75, 0.2, 13.117, 0, 13.483, 0, 1, 13.828, 0, 14.172, 0, 14.517, 0, 1, 14.789, 0, 15.061, 0.2, 15.333, 0.2, 1, 15.606, 0.2, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 8.467, 0, 1, 8.578, 0, 8.689, -5, 8.8, -5, 1, 8.983, -5, 9.167, -4.197, 9.35, -1, 1, 9.533, 2.197, 9.717, 5, 9.9, 5, 1, 10.728, 5, 11.556, 0, 12.383, 0, 1, 12.75, 0, 13.117, 0, 13.483, 0, 1, 13.828, 0, 14.172, -4, 14.517, -4, 1, 14.789, -4, 15.061, -4, 15.333, -4, 1, 15.606, -4, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, 1, 1.117, 1, 1, 1.211, 1, 1.306, 0.484, 1.4, -1, 1, 1.472, -2.135, 1.544, -3, 1.617, -3, 1, 1.744, -3, 1.872, -1.615, 2, -1, 1, 2.189, -0.091, 2.378, 0, 2.567, 0, 1, 2.656, 0, 2.744, 0, 2.833, 0, 1, 3.15, 0, 3.467, 3, 3.783, 3, 1, 4.017, 3, 4.25, -4, 4.483, -4, 1, 4.617, -4, 4.75, -0.373, 4.883, 0, 1, 5.456, 1.599, 6.028, 2, 6.6, 2, 1, 6.717, 2, 6.833, 1.664, 6.95, -1, 1, 7.017, -2.522, 7.083, -7, 7.15, -7, 1, 7.222, -7, 7.294, -3.187, 7.367, -1, 1, 7.456, 1.692, 7.544, 2, 7.633, 2, 1, 7.772, 2, 7.911, 0, 8.05, 0, 1, 8.117, 0, 8.183, 0, 8.25, 0, 1, 8.322, 0, 8.394, 0.497, 8.467, 1, 1, 8.578, 1.773, 8.689, 2, 8.8, 2, 1, 8.983, 2, 9.167, 0, 9.35, 0, 1, 9.533, 0, 9.717, 0, 9.9, 0, 1, 10.728, 0, 11.556, -2, 12.383, -2, 1, 12.75, -2, 13.117, -1.374, 13.483, 0, 1, 13.828, 1.29, 14.172, 2, 14.517, 2, 1, 14.789, 2, 15.061, 2, 15.333, 2, 1, 15.606, 2, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 8.467, 0, 1, 8.578, 0, 8.689, 2, 8.8, 2, 1, 8.983, 2, 9.167, 1, 9.35, 1, 1, 9.533, 1, 9.717, 1, 9.9, 1, 1, 10.728, 1, 11.556, 0, 12.383, 0, 1, 12.75, 0, 13.117, 0, 13.483, 0, 1, 13.828, 0, 14.172, 2, 14.517, 2, 1, 14.789, 2, 15.061, 2, 15.333, 2, 1, 15.606, 2, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, -10, 1.117, -10, 1, 1.283, -10, 1.45, -10, 1.617, -10, 1, 1.767, -10, 1.917, -9, 2.067, -9, 1, 2.3, -9, 2.533, -10, 2.767, -10, 1, 2.789, -10, 2.811, -10, 2.833, -10, 1, 3.15, -10, 3.467, -7, 3.783, -7, 1, 4.017, -7, 4.25, -10, 4.483, -10, 1, 4.617, -10, 4.75, -10, 4.883, -10, 1, 5.456, -10, 6.028, -8, 6.6, -8, 1, 6.717, -8, 6.833, -8, 6.95, -8, 1, 7.089, -8, 7.228, -1.529, 7.367, 2, 1, 7.456, 4.258, 7.544, 4, 7.633, 4, 1, 7.772, 4, 7.911, 2.984, 8.05, -1, 1, 8.117, -2.912, 8.183, -6, 8.25, -6, 1, 8.322, -6, 8.394, 3, 8.467, 3, 1, 8.578, 3, 8.689, -1, 8.8, -1, 1, 8.983, -1, 9.167, -1, 9.35, -1, 1, 9.533, -1, 9.717, -1, 9.9, -1, 1, 10.15, -1, 10.4, -1, 10.65, -1, 1, 11.228, -1, 11.806, -2, 12.383, -2, 1, 12.75, -2, 13.117, -1.91, 13.483, 0, 1, 13.828, 1.794, 14.172, 5, 14.517, 5, 1, 14.789, 5, 15.061, -9, 15.333, -9, 1, 15.606, -9, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 0.15, 0, 1, 0.472, 0, 0.794, -3, 1.117, -3, 1, 1.283, -3, 1.45, -3, 1.617, -3, 1, 1.767, -3, 1.917, 1, 2.067, 1, 1, 2.3, 1, 2.533, -3, 2.767, -3, 1, 2.789, -3, 2.811, -3, 2.833, -3, 1, 3.15, -3, 3.467, -2, 3.783, -2, 1, 4.017, -2, 4.25, -2.019, 4.483, -3, 1, 4.617, -3.561, 4.75, -8, 4.883, -8, 1, 5.456, -8, 6.028, -8, 6.6, -8, 1, 6.717, -8, 6.833, -6.416, 6.95, -4, 1, 7.089, -1.124, 7.228, 1.913, 7.367, 4, 1, 7.456, 5.336, 7.544, 5.846, 7.633, 7, 1, 7.772, 8.803, 7.911, 10, 8.05, 10, 1, 8.117, 10, 8.183, 10, 8.25, 10, 1, 8.322, 10, 8.394, 0, 8.467, 0, 1, 8.578, 0, 8.689, 10, 8.8, 10, 1, 8.983, 10, 9.167, 10, 9.35, 10, 1, 9.533, 10, 9.717, 10, 9.9, 10, 1, 10.15, 10, 10.4, 10, 10.65, 10, 1, 11.228, 10, 11.806, -10, 12.383, -10, 1, 12.75, -10, 13.117, -6.555, 13.483, -3, 1, 13.828, 0.34, 14.172, 1, 14.517, 1, 1, 14.789, 1, 15.061, -3, 15.333, -3, 1, 15.606, -3, 15.878, 0, 16.15, 0, 0, 16.65, 0]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": 0.5, "FadeOutTime": 0.5, "Segments": [0, 0, 0, 1.617, 0, 1, 1.767, 0, 1.917, 2, 2.067, 2, 1, 2.3, 2, 2.533, 0, 2.767, 0, 1, 2.789, 0, 2.811, 0, 2.833, 0, 1, 3.15, 0, 3.467, 4, 3.783, 4, 1, 4.017, 4, 4.25, 0, 4.483, 0, 1, 4.617, 0, 4.75, 0, 4.883, 0, 1, 5.456, 0, 6.028, 0.229, 6.6, 1, 1, 6.717, 1.157, 6.833, 2.146, 6.95, 3, 1, 7.089, 4.017, 7.228, 5.45, 7.367, 7, 1, 7.456, 7.992, 7.544, 9, 7.633, 9, 1, 7.772, 9, 7.911, 4, 8.05, 4, 1, 8.117, 4, 8.183, 6.352, 8.25, 8, 1, 8.322, 9.786, 8.394, 10, 8.467, 10, 1, 8.578, 10, 8.689, 10, 8.8, 10, 1, 8.983, 10, 9.167, 10, 9.35, 10, 1, 9.533, 10, 9.717, 10, 9.9, 10, 1, 10.15, 10, 10.4, 10, 10.65, 10, 1, 11.228, 10, 11.806, 5.337, 12.383, 2, 1, 12.75, -0.118, 13.117, 0, 13.483, 0, 1, 13.828, 0, 14.172, 6, 14.517, 6, 1, 14.789, 6, 15.061, 6, 15.333, 6, 1, 15.606, 6, 15.878, 0, 16.15, 0, 0, 16.65, 0]}]}