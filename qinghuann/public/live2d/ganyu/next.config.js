/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  webpack: (config) => {
    // 配置Live2D模型文件的处理
    config.module.rules.push({
      test: /\.(moc3|physics3\.json|cdi3\.json|model3\.json|motion3\.json)$/,
      type: 'asset/resource',
      generator: {
        filename: 'static/live2d/[name][ext]',
      },
    });
    
    return config;
  },
  // 配置静态文件服务
  async rewrites() {
    return [
      {
        source: '/live2d/:path*',
        destination: '/live2d/:path*',
      },
    ];
  },
};

module.exports = nextConfig; 