'use client';

import dynamic from 'next/dynamic';

// 动态导入Live2DViewer组件，禁用SSR
const Live2DViewer = dynamic(() => import('@/components/Live2DViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center gap-4">
      <div className="relative rounded-lg border border-gray-300 shadow-lg" style={{ width: 800, height: 600 }}>
        <div className="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-lg bg-black text-white">
          <div className="text-center">
            <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
            <p>正在加载Live2D组件...</p>
          </div>
        </div>
      </div>
    </div>
  ),
});

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-blue-50 to-indigo-100 py-8">
      <div className="container mx-auto px-4">
        <h1 className="mb-2 text-center text-4xl font-bold text-gray-800">甘雨 Live2D 模型展示</h1>
        <p className="mb-8 text-center text-gray-600">来自原神的可爱甘雨！</p>

        <div className="flex justify-center">
          <Live2DViewer modelPath="/live2d/ganyu/Ganyu.model3.json" width={800} height={600} />
        </div>

        <div className="mx-auto mt-8 max-w-2xl">
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-2xl font-semibold text-gray-800">功能介绍</h2>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">👀</span>
                  <div>
                    <h3 className="font-medium text-gray-800">视线跟踪</h3>
                    <p className="text-sm text-gray-600">移动鼠标，甘雨的视线会跟随你的光标移动</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🎭</span>
                  <div>
                    <h3 className="font-medium text-gray-800">动作播放</h3>
                    <p className="text-sm text-gray-600">点击"播放动作"按钮观看甘雨的动画</p>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">😊</span>
                  <div>
                    <h3 className="font-medium text-gray-800">表情切换</h3>
                    <p className="text-sm text-gray-600">点击"切换表情"看甘雨的不同表情</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-2xl">👆</span>
                  <div>
                    <h3 className="font-medium text-gray-800">互动点击</h3>
                    <p className="text-sm text-gray-600">直接点击甘雨模型进行互动</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
