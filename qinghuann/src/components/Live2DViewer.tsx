'use client';

import { useEffect, useRef, useState } from 'react';

interface Live2DViewerProps {
  modelPath: string;
  width?: number;
  height?: number;
}

export default function Live2DViewer({ modelPath, width = 800, height = 600 }: Live2DViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const appRef = useRef<any>(null);
  const modelRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState<boolean | null>(null);

  // 检查Live2D支持
  const checkLive2DSupport = () => {
    // 检查WebGL支持
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || (canvas.getContext('experimental-webgl') as WebGLRenderingContext | null);

    if (!gl) {
      return false;
    }

    // 检查Cubism Core是否已加载
    if (!(window as any).Live2DCubismCore) {
      console.warn('Live2DCubismCore not found');
      return false;
    }

    return true;
  };

  useEffect(() => {
    // 等待一小段时间确保Cubism Core已加载
    const timeoutId = setTimeout(() => {
      // 检查Live2D支持
      const supported = checkLive2DSupport();
      setIsSupported(supported);

      if (!supported) {
        setError('您的浏览器不支持WebGL或Live2D Cubism Core未加载');
        setIsLoading(false);
        return;
      }

      if (!canvasRef.current) return;

      const initLive2D = async () => {
        try {
          setIsLoading(true);
          setError(null);

          // 动态导入Live2D模块
          const [{ default: PIXI }, { Live2DModel }] = await Promise.all([import('pixi.js'), import('pixi-live2d-display')]);

          // 设置Live2D的Cubism SDK路径
          (window as any).PIXI = PIXI;

          // 初始化Cubism Core
          if ((window as any).Live2DCubismCore && !(window as any).Live2DCubismCore._isStarted) {
            (window as any).Live2DCubismCore.startUp();
            (window as any).Live2DCubismCore._isStarted = true;
          }

          // 创建PIXI应用
          const app = new PIXI.Application();
          await app.init({
            canvas: canvasRef.current!,
            width,
            height,
            backgroundColor: 0x87ceeb, // 天蓝色背景
            backgroundAlpha: 1,
          });

          appRef.current = app;

          // 加载Live2D模型
          console.log('Loading model from:', modelPath);
          const model = await Live2DModel.from(modelPath);
          modelRef.current = model;

          // 设置模型位置和大小
          const scale = Math.min(width / model.width, height / model.height) * 0.8;
          model.scale.set(scale);
          model.position.set(width / 2, height * 0.9);

          // 添加模型到舞台
          app.stage.addChild(model as any);

          // 添加鼠标交互
          model.on('hit', (hitAreas: string[]) => {
            console.log('Hit areas:', hitAreas);
            // 播放随机动作
            if ((model as any).motion) {
              try {
                (model as any).motion('Scene1');
              } catch (e) {
                console.log('Motion failed:', e);
              }
            }
          });

          // 添加眼睛追踪鼠标
          let mouseX = 0;
          let mouseY = 0;

          const onMouseMove = (event: MouseEvent) => {
            if (!canvasRef.current) return;
            const rect = canvasRef.current.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
          };

          if (canvasRef.current) {
            canvasRef.current.addEventListener('mousemove', onMouseMove);
          }

          // 眼睛追踪动画
          const ticker = PIXI.Ticker.shared;
          const trackMouse = () => {
            if (model && model.internalModel) {
              // 计算鼠标相对于模型的位置
              const modelX = model.position.x;
              const modelY = model.position.y;

              const relativeX = (mouseX - modelX) / width;
              const relativeY = (mouseY - modelY) / height;

              // 设置眼睛参数（如果模型支持）
              try {
                const coreModel = (model.internalModel as any).coreModel;
                if (coreModel && coreModel.setParameterValueById) {
                  // 尝试设置常见的眼睛参数
                  const paramIds = ['ParamAngleX', 'ParamAngleY', 'ParamEyeBallX', 'ParamEyeBallY'];
                  const values = [relativeX * 30, relativeY * 30, relativeX * 10, relativeY * 10];

                  paramIds.forEach((paramId, index) => {
                    try {
                      coreModel.setParameterValueById(paramId, values[index]);
                    } catch (e) {
                      // 忽略不存在的参数
                    }
                  });
                }
              } catch (e) {
                // 忽略参数设置错误
              }
            }
          };

          ticker.add(trackMouse);

          // 设置模型为交互式
          (model as any).interactive = true;

          console.log('Live2D model loaded successfully');
        } catch (err) {
          console.error('Failed to load Live2D model:', err);
          setError('加载Live2D模型失败：' + (err as Error).message);
        } finally {
          setIsLoading(false);
        }
      };

      initLive2D();
    }, 1000); // 等待1秒确保SDK加载完成

    // 清理函数
    return () => {
      clearTimeout(timeoutId);
      if (appRef.current) {
        appRef.current.destroy(true);
        appRef.current = null;
      }
    };
  }, [modelPath, width, height]);

  const handlePlayMotion = () => {
    if (modelRef.current) {
      try {
        // 播放动作
        (modelRef.current as any).motion('Scene1');
        console.log('Playing motion: Scene1');
      } catch (error) {
        console.log('Motion playback failed:', error);
        // 尝试播放默认动作
        try {
          (modelRef.current as any).motion();
        } catch (e) {
          console.log('Default motion failed:', e);
        }
      }
    }
  };

  const handleExpression = () => {
    if (modelRef.current) {
      try {
        // 切换表情
        const model = modelRef.current as any;
        if (model.expression) {
          const expressionCount = model.internalModel?.expressions?.length || 1;
          const randomExpression = Math.floor(Math.random() * expressionCount);
          model.expression(randomExpression);
          console.log('Changed expression to:', randomExpression);
        }
      } catch (error) {
        console.log('Expression change failed:', error);
      }
    }
  };

  if (isSupported === false) {
    return (
      <div className="flex flex-col items-center gap-4">
        <div className="relative rounded-lg border border-gray-300 shadow-lg" style={{ width, height }}>
          <div className="bg-opacity-75 absolute inset-0 flex items-center justify-center rounded-lg bg-yellow-500 text-white">
            <div className="p-4 text-center">
              <p className="mb-2">⚠️ 浏览器不支持WebGL或Live2D运行时未加载</p>
              <p className="text-sm">请刷新页面或使用支持WebGL的现代浏览器</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="relative">
        <canvas ref={canvasRef} className="rounded-lg border border-gray-300 shadow-lg" style={{ width, height }} />
        {isLoading && (
          <div className="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-lg bg-black text-white">
            <div className="text-center">
              <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
              <p>正在加载甘雨模型...</p>
            </div>
          </div>
        )}
        {error && (
          <div className="bg-opacity-75 absolute inset-0 flex items-center justify-center rounded-lg bg-red-500 p-4 text-white">
            <div className="text-center">
              <p className="mb-2">❌ 加载失败</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-4">
        <button
          onClick={handlePlayMotion}
          disabled={isLoading || !!error}
          className="rounded-md bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-400">
          播放动作
        </button>
        <button
          onClick={handleExpression}
          disabled={isLoading || !!error}
          className="rounded-md bg-purple-500 px-4 py-2 text-white transition-colors hover:bg-purple-600 disabled:cursor-not-allowed disabled:bg-gray-400">
          切换表情
        </button>
      </div>

      <div className="max-w-md text-center text-sm text-gray-600">
        <p>🖱️ 移动鼠标让甘雨的视线跟随</p>
        <p>👆 点击模型进行互动</p>
        <p>🎭 使用按钮播放动作和表情</p>
      </div>
    </div>
  );
}
