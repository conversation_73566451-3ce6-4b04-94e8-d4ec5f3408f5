'use client';

import { useRef } from 'react';
import { animate, svg } from 'animejs';

export default function ScrollPathAnimation() {
  const containerRef = useRef<HTMLDivElement>(null);
  const pathRef = useRef<SVGPathElement>(null);
  const itemRef = useRef<HTMLDivElement>(null);

  const startAnimation = () => {
    if (pathRef.current && itemRef.current) {
      // 路径绘制动画
      animate(svg.createDrawable(pathRef.current), {
        draw: '0 1',
        ease: 'linear',
        duration: 5000,
        autoplay: true,
      });

      // 物体沿路径运动动画
      animate(itemRef.current, {
        ease: 'linear',
        duration: 5000,
        autoplay: true,
        ...svg.createMotionPath(pathRef.current),
      });
    }
  };

  return (
    <div ref={containerRef} className="relative min-h-screen bg-gray-50">
      {/* 控制按钮 */}
      <div className="fixed top-4 left-4 z-40 flex gap-2">
        <button
          onClick={startAnimation}
          className="rounded-lg bg-green-500 px-4 py-2 font-medium text-white transition-colors hover:bg-green-600 disabled:cursor-not-allowed disabled:bg-gray-400">
          开始动画
        </button>
      </div>

      {/* 固定的SVG容器 */}
      <div className="pointer-events-none fixed inset-0 z-30 flex items-center justify-center">
        <svg className="box-border h-auto w-full p-16" viewBox="0 0 354 179" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            ref={pathRef}
            d="M353.5 12.5C289.5 -14.4999 241.227 12.7898 229.5 36.0002C205.5 83.5 361 84 327.5 134.5C300.7 174.9 100.5 180.667 1 177.5"
            stroke="red"
            strokeWidth="3"
            fill="none"
            strokeDasharray="1000"
            strokeDashoffset="1000"
            strokeLinecap="round"
          />
        </svg>
      </div>

      {/* 运动的小球 */}
      <div ref={itemRef} className="h-4 w-4 translate-x-[50px] translate-y-[150px] rounded-full bg-blue-500 shadow-lg"></div>

      <div className="h-screen w-screen"></div>
      <div className="h-screen w-screen"></div>
      <div className="h-screen w-screen"></div>
      <div className="h-screen w-screen"></div>
      <div className="h-screen w-screen"></div>
    </div>
  );
}
