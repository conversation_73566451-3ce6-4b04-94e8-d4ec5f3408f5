'use client';

import { useRef, useState, useEffect } from 'react';
import { animate } from 'animejs';

// 拖尾粒子接口
interface TrailParticle {
  id: number;
  x: number;
  y: number;
  opacity: number;
  size: number;
  delay: number;
  type: 'glow' | 'spark' | 'dot' | 'ring' | 'beam' | 'wave' | 'lightning';
  velocity: { x: number; y: number };
  life: number;
  maxLife: number;
  color: string;
  rotation: number;
  scale: number;
}

const LightBar = () => {
  const orbRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [trailParticles, setTrailParticles] = useState<TrailParticle[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [mouseFollowMode, setMouseFollowMode] = useState(false);
  const particleIdRef = useRef(0);
  const animationRef = useRef<any>(null);
  const mousePositionRef = useRef({ x: 0, y: 0 });

  // 自定义CSS样式
  const lightStyles = `
    @keyframes trail-fade {
      0% { 
        opacity: 1; 
        transform: scale(1) translateZ(0); 
        filter: blur(1px);
      }
      50% {
        opacity: 0.6;
        transform: scale(0.7) translateZ(0);
        filter: blur(2px);
      }
      100% { 
        opacity: 0; 
        transform: scale(0.2) translateZ(0); 
        filter: blur(4px);
      }
    }
    
    @keyframes spark-fade {
      0% { 
        opacity: 1; 
        transform: scale(1) rotate(0deg); 
        filter: blur(0px);
      }
      50% {
        opacity: 0.8;
        transform: scale(0.8) rotate(180deg);
        filter: blur(1px);
      }
      100% { 
        opacity: 0; 
        transform: scale(0.3) rotate(360deg); 
        filter: blur(2px);
      }
    }
    
    @keyframes dot-fade {
      0% { 
        opacity: 1; 
        transform: scale(1); 
        box-shadow: 0 0 10px currentColor;
      }
      100% { 
        opacity: 0; 
        transform: scale(0.1); 
        box-shadow: 0 0 20px currentColor;
      }
    }
    
    @keyframes ring-expand {
      0% { 
        opacity: 0.8; 
        transform: scale(0.5); 
        border-width: 2px;
      }
      50% {
        opacity: 0.4;
        transform: scale(1.2);
        border-width: 1px;
      }
      100% { 
        opacity: 0; 
        transform: scale(2); 
        border-width: 0px;
      }
    }
    
    @keyframes beam-stretch {
      0% { 
        opacity: 1; 
        transform: scaleX(0) scaleY(1) rotate(var(--rotation, 0deg));
        filter: blur(0px) brightness(2);
      }
      30% {
        opacity: 0.9;
        transform: scaleX(1) scaleY(0.8) rotate(var(--rotation, 0deg));
        filter: blur(1px) brightness(1.8);
      }
      100% { 
        opacity: 0; 
        transform: scaleX(1.5) scaleY(0.3) rotate(var(--rotation, 0deg));
        filter: blur(3px) brightness(1);
      }
    }
    
    @keyframes wave-ripple {
      0% { 
        opacity: 0.8; 
        transform: scale(0.3) rotate(0deg);
        border-radius: 50%;
        filter: blur(0px);
      }
      50% {
        opacity: 0.5;
        transform: scale(1.5) rotate(180deg);
        border-radius: 30%;
        filter: blur(2px);
      }
      100% { 
        opacity: 0; 
        transform: scale(3) rotate(360deg);
        border-radius: 10%;
        filter: blur(5px);
      }
    }
    
    @keyframes lightning-flash {
      0% { 
        opacity: 1; 
        transform: scale(1) rotate(var(--rotation, 0deg));
        filter: brightness(3) blur(0px);
      }
      20% {
        opacity: 0.8;
        transform: scale(1.2) rotate(var(--rotation, 0deg));
        filter: brightness(2.5) blur(1px);
      }
      40% {
        opacity: 0.3;
        transform: scale(0.8) rotate(var(--rotation, 0deg));
        filter: brightness(2) blur(2px);
      }
      60% {
        opacity: 0.6;
        transform: scale(1.1) rotate(var(--rotation, 0deg));
        filter: brightness(2.2) blur(1px);
      }
      100% { 
        opacity: 0; 
        transform: scale(0.5) rotate(var(--rotation, 0deg));
        filter: brightness(1) blur(3px);
      }
    }
    
    @keyframes glow-pulse {
      0%, 100% { 
        box-shadow: 
          0 0 20px rgba(16, 185, 129, 0.6), 
          0 0 40px rgba(16, 185, 129, 0.4), 
          0 0 60px rgba(16, 185, 129, 0.2),
          inset 0 0 10px rgba(16, 185, 129, 0.3);
      }
      50% { 
        box-shadow: 
          0 0 30px rgba(16, 185, 129, 0.8), 
          0 0 60px rgba(16, 185, 129, 0.6), 
          0 0 90px rgba(16, 185, 129, 0.4),
          inset 0 0 15px rgba(16, 185, 129, 0.5);
      }
    }
    
    @keyframes orb-float {
      0%, 100% { transform: translateY(0px) scale(1); }
      50% { transform: translateY(-3px) scale(1.05); }
    }
    
    @keyframes energy-ripple {
      0% { 
        transform: scale(0.8); 
        opacity: 0.8; 
      }
      100% { 
        transform: scale(2.5); 
        opacity: 0; 
      }
    }
    
    .glow-orb {
      animation: glow-pulse 2s ease-in-out infinite, orb-float 3s ease-in-out infinite;
    }
    
    .particle-glow {
      animation: trail-fade 1.2s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-spark {
      animation: spark-fade 0.8s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-dot {
      animation: dot-fade 1s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-ring {
      animation: ring-expand 1.5s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-beam {
      animation: beam-stretch 1s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-wave {
      animation: wave-ripple 2s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .particle-lightning {
      animation: lightning-flash 0.6s ease-out forwards;
      will-change: transform, opacity;
    }
    
    .core-orb {
      background: radial-gradient(
        circle at 30% 30%, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(16, 185, 129, 0.8) 20%, 
        rgba(5, 150, 105, 0.9) 60%, 
        rgba(4, 120, 87, 1) 100%
      );
      box-shadow: 
        inset 0 0 8px rgba(255, 255, 255, 0.3),
        0 0 20px rgba(16, 185, 129, 0.6),
        0 0 40px rgba(16, 185, 129, 0.3);
    }
    
    .energy-ripple {
      animation: energy-ripple 2s ease-out infinite;
    }
  `;

  // 创建拖尾粒子
  const createTrailParticle = (x: number, y: number, velocityX = 0, velocityY = 0) => {
    const particleTypes: Array<TrailParticle['type']> = ['glow', 'spark', 'dot', 'ring'];
    const colors = ['emerald-400', 'emerald-300', 'green-400', 'teal-400', 'cyan-400'];

    // 根据概率选择粒子类型 - 模拟专业视频特效
    let type: TrailParticle['type'];
    const rand = Math.random();
    if (rand < 0.25)
      type = 'glow'; // 25% - 基础发光
    else if (rand < 0.4)
      type = 'spark'; // 15% - 火花
    else if (rand < 0.55)
      type = 'dot'; // 15% - 光点
    else if (rand < 0.7)
      type = 'ring'; // 15% - 光环
    else if (rand < 0.85)
      type = 'beam'; // 15% - 光束
    else if (rand < 0.95)
      type = 'wave'; // 10% - 能量波
    else type = 'lightning'; // 5% - 闪电效果

    const particle: TrailParticle = {
      id: particleIdRef.current++,
      x,
      y,
      opacity: Math.random() * 0.7 + 0.3, // 0.3-1.0 更强的透明度
      size:
        type === 'beam'
          ? Math.random() * 20 + 10
          : type === 'wave'
            ? Math.random() * 15 + 8
            : type === 'lightning'
              ? Math.random() * 12 + 6
              : type === 'ring'
                ? Math.random() * 8 + 6
                : Math.random() * 6 + 2,
      delay: Math.random() * 0.2, // 减少延迟让效果更连贯
      type,
      velocity: {
        x: velocityX + (Math.random() - 0.5) * 3, // 增加速度变化
        y: velocityY + (Math.random() - 0.5) * 3,
      },
      life: 0,
      maxLife: type === 'lightning' ? 600 : type === 'beam' ? 1000 : type === 'wave' ? 2000 : type === 'ring' ? 1500 : type === 'spark' ? 800 : 1200,
      color: colors[Math.floor(Math.random() * colors.length)],
      rotation: Math.random() * 360,
      scale: Math.random() * 0.8 + 0.4, // 0.4-1.2 更大的缩放范围
    };

    setTrailParticles((prev) => [...prev.slice(-35), particle]); // 增加到36个粒子

    // 根据粒子类型设置不同的生命周期
    setTimeout(() => {
      setTrailParticles((prev) => prev.filter((p) => p.id !== particle.id));
    }, particle.maxLife);
  };

  // 鼠标移动处理
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!mouseFollowMode || !containerRef.current || !orbRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - containerRect.left - containerRect.width / 2;
    const y = e.clientY - containerRect.top - containerRect.height / 2;

    // 计算移动速度
    const prevPos = mousePositionRef.current;
    const velocityX = (x - prevPos.x) * 0.1;
    const velocityY = (y - prevPos.y) * 0.1;

    mousePositionRef.current = { x, y };

    // 使用 anime.js 平滑移动到鼠标位置
    animate(orbRef.current, {
      translateX: x,
      translateY: y,
      duration: 200, // 减少延迟让跟随更灵敏
      easing: 'easeOutQuad',
    });

    // 根据移动速度创建不同数量的拖尾粒子
    const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
    const particleCount = Math.min(Math.floor(speed * 2) + 1, 4);

    for (let i = 0; i < particleCount; i++) {
      if (Math.random() < 0.8) {
        // 80% 概率生成粒子
        const offsetX = (Math.random() - 0.5) * 15;
        const offsetY = (Math.random() - 0.5) * 15;
        const delay = i * 50; // 错开生成时间

        setTimeout(() => {
          createTrailParticle(
            e.clientX - containerRect.left + offsetX,
            e.clientY - containerRect.top + offsetY,
            -velocityX * (0.5 + Math.random() * 0.5), // 反向速度模拟拖尾
            -velocityY * (0.5 + Math.random() * 0.5)
          );
        }, delay);
      }
    }
  };

  // 开始浮动动画
  const startFloatingAnimation = () => {
    if (orbRef.current && !isAnimating) {
      setIsAnimating(true);
      setMouseFollowMode(false);

      // 创建循环浮动动画
      const floatAnimation = () => {
        if (orbRef.current) {
          animationRef.current = animate(orbRef.current, {
            translateX: [
              { value: Math.random() * 200 - 100, duration: 2000 },
              { value: Math.random() * 200 - 100, duration: 2000 },
              { value: Math.random() * 200 - 100, duration: 2000 },
              { value: 0, duration: 2000 },
            ],
            translateY: [
              { value: Math.random() * 100 - 50, duration: 2000 },
              { value: Math.random() * 100 - 50, duration: 2000 },
              { value: Math.random() * 100 - 50, duration: 2000 },
              { value: 0, duration: 2000 },
            ],
            easing: 'easeInOutQuad',
            loop: true,
            update: () => {
              // 在动画过程中创建拖尾粒子
              if (orbRef.current && Math.random() < 0.6) {
                const rect = orbRef.current.getBoundingClientRect();
                const containerRect = containerRef.current?.getBoundingClientRect();
                if (containerRect) {
                  const offsetX = (Math.random() - 0.5) * 12;
                  const offsetY = (Math.random() - 0.5) * 12;

                  // 创建多个粒子形成更丰富的拖尾
                  for (let i = 0; i < 2; i++) {
                    setTimeout(() => {
                      createTrailParticle(
                        rect.left - containerRect.left + rect.width / 2 + offsetX,
                        rect.top - containerRect.top + rect.height / 2 + offsetY,
                        (Math.random() - 0.5) * 1.5, // 随机速度
                        (Math.random() - 0.5) * 1.5
                      );
                    }, i * 100);
                  }
                }
              }
            },
          });
        }
      };

      floatAnimation();
    }
  };

  // 开始鼠标跟随模式
  const startMouseFollow = () => {
    if (animationRef.current) {
      animationRef.current.pause();
      animationRef.current = null;
    }
    setIsAnimating(false);
    setMouseFollowMode(true);
  };

  // 停止所有动画
  const stopAnimation = () => {
    if (animationRef.current) {
      animationRef.current.pause();
      animationRef.current = null;
    }
    setIsAnimating(false);
    setMouseFollowMode(false);
    setTrailParticles([]);

    // 回到中心位置
    if (orbRef.current) {
      animate(orbRef.current, {
        translateX: 0,
        translateY: 0,
        duration: 500,
        easing: 'easeOutQuad',
      });
    }
  };

  return (
    <div
      ref={containerRef}
      className="relative h-96 w-full cursor-crosshair overflow-hidden rounded-lg bg-gradient-to-br from-gray-900 via-gray-800 to-black"
      onMouseMove={handleMouseMove}>
      {/* 注入自定义样式 */}
      <style dangerouslySetInnerHTML={{ __html: lightStyles }} />

      {/* 控制按钮 */}
      <div className="absolute top-4 left-4 z-40 flex gap-2">
        <button
          onClick={startFloatingAnimation}
          disabled={isAnimating}
          className="rounded-lg bg-emerald-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-emerald-600 disabled:cursor-not-allowed disabled:bg-gray-400">
          自动浮动
        </button>
        <button
          onClick={startMouseFollow}
          disabled={mouseFollowMode}
          className="rounded-lg bg-purple-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-purple-600 disabled:cursor-not-allowed disabled:bg-gray-400">
          跟随鼠标
        </button>
        <button onClick={stopAnimation} className="rounded-lg bg-red-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-600">
          停止
        </button>
      </div>

      {/* 背景星点 */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-1 w-1 animate-pulse rounded-full bg-white/20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* 主光球 */}
      <div ref={orbRef} className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <div className="relative">
          {/* 外层光晕 */}
          <div className="glow-orb absolute h-16 w-16 -translate-x-8 -translate-y-8 rounded-full bg-emerald-400/20 blur-xl"></div>

          {/* 中层光晕 */}
          <div className="absolute h-12 w-12 -translate-x-6 -translate-y-6 animate-pulse rounded-full bg-emerald-300/40 blur-lg"></div>

          {/* 核心光球 */}
          <div className="core-orb relative h-8 w-8 -translate-x-4 -translate-y-4 rounded-full">
            {/* 内部高光 */}
            <div className="absolute top-1.5 left-1.5 h-3 w-3 rounded-full bg-gradient-to-br from-white/95 to-emerald-100/70 blur-[0.5px]"></div>
            {/* 边缘反光 */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent via-transparent to-white/30"></div>
            {/* 底部阴影 */}
            <div className="absolute bottom-0 left-1/2 h-1 w-4 -translate-x-1/2 rounded-full bg-emerald-900/20 blur-sm"></div>
          </div>

          {/* 能量波纹 */}
          <div className="energy-ripple absolute h-8 w-8 -translate-x-4 -translate-y-4 rounded-full border border-emerald-400/40"></div>
          <div className="energy-ripple absolute h-8 w-8 -translate-x-4 -translate-y-4 rounded-full border border-emerald-400/30" style={{ animationDelay: '0.5s' }}></div>

          {/* 闪烁粒子效果 */}
          <div className="absolute h-20 w-20 -translate-x-10 -translate-y-10 animate-ping rounded-full bg-emerald-400/8"></div>
          <div
            className="absolute h-24 w-24 -translate-x-12 -translate-y-12 animate-ping rounded-full bg-emerald-400/4"
            style={{ animationDelay: '0.7s', animationDuration: '3s' }}></div>

          {/* 旋转光环 */}
          <div
            className="absolute h-12 w-12 -translate-x-6 -translate-y-6 animate-spin rounded-full border border-dashed border-emerald-400/30"
            style={{ animationDuration: '8s' }}></div>
        </div>
      </div>

      {/* 动态拖尾粒子 */}
      <div className="pointer-events-none absolute inset-0 z-20">
        {trailParticles.map((particle, index) => {
          const baseStyle = {
            left: `${particle.x - particle.size / 2}px`,
            top: `${particle.y - particle.size / 2}px`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDelay: `${particle.delay}s`,
          };

          // 根据粒子类型渲染不同效果
          switch (particle.type) {
            case 'glow':
              return (
                <div
                  key={particle.id}
                  className="particle-glow absolute rounded-full"
                  style={{
                    ...baseStyle,
                    background: `radial-gradient(
                      circle at 40% 40%, 
                      rgba(255, 255, 255, ${particle.opacity * 0.4}) 0%, 
                      rgba(16, 185, 129, ${particle.opacity * 0.9}) 15%, 
                      rgba(5, 150, 105, ${particle.opacity * 0.7}) 40%, 
                      rgba(4, 120, 87, ${particle.opacity * 0.5}) 70%, 
                      transparent 100%
                    )`,
                    filter: `blur(${Math.max(1, particle.size * 0.2)}px)`,
                    boxShadow: `0 0 ${particle.size * 2}px rgba(16, 185, 129, ${particle.opacity * 0.6})`,
                  }}
                />
              );

            case 'spark':
              return (
                <div
                  key={particle.id}
                  className="particle-spark absolute"
                  style={{
                    ...baseStyle,
                    background: `linear-gradient(45deg, 
                      rgba(255, 255, 255, ${particle.opacity}) 0%,
                      rgba(16, 185, 129, ${particle.opacity * 0.8}) 50%,
                      transparent 100%
                    )`,
                    clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
                    filter: `brightness(1.5) blur(${particle.size * 0.1}px)`,
                  }}
                />
              );

            case 'dot':
              return (
                <div
                  key={particle.id}
                  className={`particle-dot absolute rounded-full bg-${particle.color}`}
                  style={{
                    ...baseStyle,
                    boxShadow: `0 0 ${particle.size * 3}px currentColor, inset 0 0 ${particle.size}px rgba(255,255,255,0.3)`,
                    filter: `brightness(1.2)`,
                  }}
                />
              );

            case 'ring':
              return (
                <div
                  key={particle.id}
                  className={`particle-ring absolute rounded-full border-${particle.color}`}
                  style={{
                    ...baseStyle,
                    borderStyle: 'solid',
                    borderWidth: '2px',
                    background: 'transparent',
                    filter: `drop-shadow(0 0 ${particle.size}px rgba(16, 185, 129, ${particle.opacity}))`,
                  }}
                />
              );

            case 'beam':
              return (
                <div
                  key={particle.id}
                  className="particle-beam absolute"
                  style={
                    {
                      ...baseStyle,
                      background: `linear-gradient(${particle.rotation}deg, 
                      rgba(255, 255, 255, ${particle.opacity}) 0%,
                      rgba(16, 185, 129, ${particle.opacity * 0.9}) 30%,
                      rgba(5, 150, 105, ${particle.opacity * 0.7}) 70%,
                      transparent 100%
                    )`,
                      borderRadius: '50% 10%',
                      '--rotation': `${particle.rotation}deg`,
                      filter: `brightness(2) blur(${particle.size * 0.05}px)`,
                      boxShadow: `0 0 ${particle.size}px rgba(16, 185, 129, ${particle.opacity * 0.8})`,
                    } as React.CSSProperties
                  }
                />
              );

            case 'wave':
              return (
                <div
                  key={particle.id}
                  className="particle-wave absolute"
                  style={{
                    ...baseStyle,
                    background: `conic-gradient(
                      from ${particle.rotation}deg,
                      rgba(255, 255, 255, ${particle.opacity * 0.8}) 0deg,
                      rgba(16, 185, 129, ${particle.opacity}) 90deg,
                      rgba(5, 150, 105, ${particle.opacity * 0.6}) 180deg,
                      rgba(4, 120, 87, ${particle.opacity * 0.4}) 270deg,
                      transparent 360deg
                    )`,
                    borderRadius: '30%',
                    filter: `blur(${Math.max(2, particle.size * 0.15)}px) brightness(1.3)`,
                    boxShadow: `0 0 ${particle.size * 1.5}px rgba(16, 185, 129, ${particle.opacity * 0.5})`,
                  }}
                />
              );

            case 'lightning':
              return (
                <div
                  key={particle.id}
                  className="particle-lightning absolute"
                  style={
                    {
                      ...baseStyle,
                      background: `linear-gradient(${particle.rotation}deg, 
                      rgba(255, 255, 255, ${particle.opacity}) 0%,
                      rgba(16, 185, 129, ${particle.opacity * 0.9}) 20%,
                      rgba(255, 255, 255, ${particle.opacity * 0.7}) 40%,
                      rgba(16, 185, 129, ${particle.opacity * 0.8}) 60%,
                      transparent 100%
                    )`,
                      clipPath: 'polygon(50% 0%, 65% 25%, 85% 25%, 60% 50%, 75% 75%, 50% 60%, 25% 75%, 40% 50%, 15% 25%, 35% 25%)',
                      '--rotation': `${particle.rotation}deg`,
                      filter: `brightness(3) blur(${particle.size * 0.05}px) saturate(1.5)`,
                      boxShadow: `0 0 ${particle.size * 2}px rgba(255, 255, 255, ${particle.opacity}), 0 0 ${particle.size * 4}px rgba(16, 185, 129, ${particle.opacity * 0.6})`,
                    } as React.CSSProperties
                  }
                />
              );

            default:
              return null;
          }
        })}
      </div>

      {/* 状态指示器 */}
      <div className="absolute right-4 bottom-4 text-sm text-emerald-400">{isAnimating ? '🌟 自动浮动中...' : mouseFollowMode ? '🎯 跟随鼠标中...' : '💫 待机中'}</div>

      {/* 鼠标跟随提示 */}
      {mouseFollowMode && (
        <div className="pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="animate-pulse text-center text-xs text-emerald-300/60">移动鼠标控制光球</div>
        </div>
      )}
    </div>
  );
};

export default LightBar;
