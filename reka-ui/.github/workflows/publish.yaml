name: Publish Docs

on:
  push:
    paths:
      - 'docs/**'

jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    name: Publish to Cloudflare Pages
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup (Install node & pnpm)
        uses: ./.github/actions/setup

      - name: Build Reka UI
        run: pnpm --filter reka-ui build

      - name: Build Docs
        run: pnpm docs:build

      # Run a action to publish docs
      - name: Publish to Cloudflare Pages
        uses: cloudflare/pages-action@v1.5.0
        with:
          apiToken: ${{ secrets.CLOUDFLARE_UNOVUE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_UNOVUE_ACCOUNT_ID }}
          projectName: reka-ui
          directory: .vitepress/dist
          # Optional: Enable this if you want to have GitHub Deployments triggered
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          # Optional: Switch what branch you are publishing to.
          # By default this will be the branch which triggered this workflow
          # TODO: remove the following branch target after merging to main
          branch: main
          # Optional: Change the working directory
          workingDirectory: docs
          wranglerVersion: '3'
