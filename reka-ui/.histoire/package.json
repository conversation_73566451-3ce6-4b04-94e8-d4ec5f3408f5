{"name": "histoire", "type": "module", "version": "0.0.2", "private": true, "scripts": {"story:dev": "histoire dev", "story:build": "histoire build", "story:preview": "histoire preview"}, "peerDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vueuse/core": "^12.0.0", "@vueuse/shared": "^12.0.0", "vite": "^5.4.19", "vue": "^3.5.13"}, "devDependencies": {"@antfu/eslint-config": "^4.13.1", "@floating-ui/dom": "^1.6.13", "@floating-ui/vue": "^1.1.6", "@histoire/plugin-vue": "^0.17.17", "@iconify/vue": "^4.3.0", "@radix-ui/colors": "^3.0.0", "@rollup/plugin-alias": "^5.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.27.0", "histoire": "^0.17.17", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "1.0.7"}}