@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .accordion-item {
    @apply mt-px overflow-hidden first:mt-0 first:rounded-t last:rounded-b focus-within:relative focus-within:z-10 focus-within:ring-2 focus-within:ring-black;
  }

  .accordion-trigger {
    @apply flex h-12 flex-1 cursor-pointer items-center justify-between bg-white px-5 text-base font-medium leading-none text-green-700 shadow-[0_1px_0] shadow-[--line-color] outline-none hover:bg-gray-200;
  }

  .accordion-content {
    @apply overflow-hidden bg-gray-100 text-sm text-gray-900 px-5 py-4;
  }
  .accordion-animated-content {
    @apply overflow-hidden bg-gray-100 text-sm text-gray-900;
  }
}

/* center all element inside stories */
.histoire-generic-render-story:not(.__histoire-render-custom-controls) [data-v-app] {
  @apply flex justify-center;
}

/* span the width to full if child has w-full class */
.histoire-generic-render-story:not(.__histoire-render-custom-controls) [data-v-app] > div:has(div.w-full) {
  width: 100%;
}