@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Geist";
  src: url("/fonts/Geist[wght].ttf");
}
@font-face {
  font-family: "Geist<PERSON>ono";
  src: url("/fonts/GeistMono[wght].ttf");
}

@layer base {
   :root {
		--font-geist-sans: "<PERSON>ei<PERSON>";
		--font-geist-mono: "GeistMono";

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 60 8% 97%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 40 6% 91%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --code: 24 9.8% 10%; /*  0 0% 4%; */
    --border:240 5.9% 90%;
    --input:240 5.9% 90%;
    --ring:142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background:141 17% 5%;
    --foreground:0 0% 95%;

    --card:24 9.8% 10%;
    --card-foreground:0 0% 95%;

    --popover:0 0% 9%;
    --popover-foreground:0 0% 95%;

    --primary: 151 62% 59%;
    --primary-foreground:144.9 80.4% 10%;

    --secondary:240 3.7% 15.9%;
    --secondary-foreground:0 0% 98%;

    --muted:0 0% 15%;
    --muted-foreground:168 2.6% 62.9%;

    --accent:12 6.5% 15.1%;
    --accent-foreground:0 0% 98%;

    --destructive:0 62.8% 30.6%;
    --destructive-foreground:0 85.7% 97.3%;

    --border:240 3.7% 15.9%;
    --input:240 3.7% 15.9%;
    --ring:142.4 71.8% 29.2%;
  }

	html {
		-webkit-text-size-adjust: 100%;
		font-variation-settings: normal;
	}

  html.dark {
    color-scheme: dark;
  }

	body {
		@apply bg-background text-foreground min-h-screen antialiased;
		/* font-feature-settings: "rlig" 1, "calt" 1; */
		font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
	}

	/* Mobile tap highlight */
	/* https://developer.mozilla.org/en-US/docs/Web/CSS/-webkit-tap-highlight-color */
	html {
		-webkit-tap-highlight-color: rgba(128, 128, 128, 0.5);
	}

  *:focus-visible {
    @apply outline-primary;
  }

  ::selection {
    background: hsl(var(--primary) / 0.3);
  }


}


@layer utilities {
	.step {
		counter-increment: step;
	}

	.step:before {
		@apply absolute w-9 h-9 bg-muted rounded-full font-mono font-medium text-center text-base inline-flex items-center justify-center -indent-px border-4 border-background;
		@apply -ml-[50px] -mt-1;
		content: counter(step);
	}

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@media (max-width: 640px) {
	.container {
		@apply px-4;
	}
}

.custom .vp-code {
  @apply border-none rounded-none;
}
.vp-code {
  @apply px-6 py-4 my-0 bg-[hsl(24_9.8%_10%)] border border-[hsl(0_0%_15%)] rounded-lg;

  .diff {
    @apply inline-block w-[calc(100%_+_48px)] px-6 -mx-6
  }
  .diff:before {
    @apply absolute left-2.5
  }

  .diff.remove {
    @apply bg-red-500/20 opacity-70
  }
  .diff.remove:before {
    content: "-";
    @apply text-red-500/80
  }

  .diff.add {
    @apply bg-green-600/15
  }
  .diff.add:before {
    content: "+";
    @apply text-green-600/80
  }

  .highlighted {
    @apply inline-block w-[calc(100%_+_48px)] px-6 -mx-6 bg-[hsl(0_0%_15%)]
  }

  code {
    @apply w-fit block min-w-full
  }
}



.custom-block {
  @apply rounded-xl p-6 border border-muted;
}

input {
  @apply bg-foreground;
}

kbd {
  @apply font-sans rounded-md px-1.5 py-[1px];
}

mark {
  @apply bg-transparent text-foreground font-semibold;
}

[class*="language"] {
  @apply relative mb-4;

  & .lang {
    @apply hidden;
  }

  /* Better impl for custom copy button */
  & .copy {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='rgba(128,128,128,1)' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24'%3E%3Crect width='8' height='4' x='8' y='2' rx='1' ry='1'/%3E%3Cpath d='M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2'/%3E%3C/svg%3E");
    background-size: 16px;
    background-position: 50% center;
    background-repeat: no-repeat;
    transition: border-color 0.25s ease 0s, background-color 0.25s ease 0s, opacity 0.25s ease 0s;
    @apply absolute top-2 right-2 w-9 h-9 rounded-lg;
  }

  & .copied {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxZW0iIGhlaWdodD0iMWVtIiB2aWV3Qm94PSIwIDAgMjQgMjQiPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzg4ODg4OCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjIiIGQ9Ik0yMCA2TDkgMTdsLTUtNSIvPjwvc3ZnPg==");
  }
}

@layer utilities {
	.step {
		counter-increment: step;
	}

	.step:before {
		@apply absolute w-9 h-9 bg-muted rounded-lg font-mono font-medium text-center text-base inline-flex items-center justify-center -indent-px border-4 border-background;
		@apply -ml-[50px] -mt-1;
		content: counter(step);
	}
}

.image-bg {
  @apply scale-75 !transform !-translate-x-1/2 !-translate-y-1/2;
}

.custom-justify-center:before,
.custom-justify-center:after {
  content: "";
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
