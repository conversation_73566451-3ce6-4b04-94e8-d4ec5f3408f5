<script setup lang="ts">
import AccordionDemo from './demo/Accordion/tailwind/index.vue'
import AlertDialogDemo from './demo/AlertDialog/tailwind/index.vue'
import AspectRatioDemo from './demo/AspectRatio/tailwind/index.vue'
import AvatarDemo from './demo/Avatar/tailwind/index.vue'
import CalendarDemo from './demo/Calendar/tailwind/index.vue'
import CheckboxDemo from './demo/Checkbox/tailwind/index.vue'
import CollapsibleDemo from './demo/Collapsible/tailwind/index.vue'
import ComboboxDemo from './demo/Combobox/tailwind/index.vue'
import ContextMenuDemo from './demo/ContextMenu/tailwind/index.vue'
import DateFieldDemo from './demo/DateField/tailwind/index.vue'
import DatePickerDemo from './demo/DatePicker/tailwind/index.vue'
import DateRangeFieldDemo from './demo/DateRangeField/tailwind/index.vue'
import DateRangePickerDemo from './demo/DateRangePicker/tailwind/index.vue'
import DialogDemo from './demo/Dialog/tailwind/index.vue'
import DropdownMenuDemo from './demo/DropdownMenu/tailwind/index.vue'
import EditableDemo from './demo/Editable/tailwind/index.vue'
import HoverCardDemo from './demo/HoverCard/tailwind/index.vue'
import LabelDemo from './demo/Label/tailwind/index.vue'
import MenubarDemo from './demo/Menubar/tailwind/index.vue'
import NavigationMenuDemo from './demo/NavigationMenu/tailwind/index.vue'
import NumberFieldDemo from './demo/NumberField/tailwind/index.vue'
import PaginationDemo from './demo/Pagination/tailwind/index.vue'
import PinInputDemo from './demo/PinInput/tailwind/index.vue'
import PopoverDemo from './demo/Popover/tailwind/index.vue'
import ProgressDemo from './demo/Progress/tailwind/index.vue'
import RadioGroupDemo from './demo/RadioGroup/tailwind/index.vue'
import RangeCalendarDemo from './demo/RangeCalendar/tailwind/index.vue'
import ScrollAreaDemo from './demo/ScrollArea/tailwind/index.vue'
import SelectDemo from './demo/Select/tailwind/index.vue'
import SeparatorDemo from './demo/Separator/tailwind/index.vue'
import SliderDemo from './demo/Slider/tailwind/index.vue'
import SplitterDemo from './demo/Splitter/tailwind/index.vue'
import StepperDemo from './demo/Stepper/tailwind/index.vue'
import SwitchDemo from './demo/Switch/tailwind/index.vue'
import TabsDemo from './demo/Tabs/tailwind/index.vue'
import TagsInputDemo from './demo/TagsInput/tailwind/index.vue'
import ToastDemo from './demo/Toast/tailwind/index.vue'
import ToggleDemo from './demo/Toggle/tailwind/index.vue'
import ToggleGroupDemo from './demo/ToggleGroup/tailwind/index.vue'
import ToolbarDemo from './demo/Toolbar/tailwind/index.vue'
import TooltipDemo from './demo/Tooltip/tailwind/index.vue'
import TreeDemo from './demo/Tree/tailwind/index.vue'

import DemoContainer from './DemoContainer.vue'
</script>

<template>
  <div>
    <DemoContainer title="accordion">
      <AccordionDemo />
    </DemoContainer>
    <DemoContainer title="alert dialog">
      <AlertDialogDemo />
    </DemoContainer>
    <DemoContainer title="aspect ratio">
      <AspectRatioDemo />
    </DemoContainer>
    <DemoContainer title="avatar">
      <AvatarDemo />
    </DemoContainer>
    <DemoContainer title="calendar">
      <CalendarDemo />
    </DemoContainer>
    <DemoContainer title="checkbox">
      <CheckboxDemo />
    </DemoContainer>
    <DemoContainer title="collapsible">
      <CollapsibleDemo />
    </DemoContainer>
    <DemoContainer title="combobox">
      <ComboboxDemo />
    </DemoContainer>
    <DemoContainer title="context menu">
      <ContextMenuDemo />
    </DemoContainer>
    <DemoContainer title="date field">
      <DateFieldDemo />
    </DemoContainer>
    <DemoContainer title="date picker">
      <DatePickerDemo />
    </DemoContainer>
    <DemoContainer title="date range field">
      <DateRangeFieldDemo />
    </DemoContainer>
    <DemoContainer title="date range picker">
      <DateRangePickerDemo />
    </DemoContainer>
    <DemoContainer title="dialog">
      <DialogDemo />
    </DemoContainer>
    <DemoContainer title="dropdown menu">
      <DropdownMenuDemo />
    </DemoContainer>
    <DemoContainer title="editable">
      <EditableDemo />
    </DemoContainer>
    <DemoContainer title="hover card">
      <HoverCardDemo />
    </DemoContainer>
    <DemoContainer title="label">
      <LabelDemo />
    </DemoContainer>
    <DemoContainer title="menubar">
      <MenubarDemo />
    </DemoContainer>
    <DemoContainer title="navigation menu">
      <NavigationMenuDemo />
    </DemoContainer>
    <DemoContainer title="number field">
      <NumberFieldDemo />
    </DemoContainer>
    <DemoContainer
      title="pagination"
      overflow
    >
      <PaginationDemo />
    </DemoContainer>
    <DemoContainer title="pin input">
      <PinInputDemo />
    </DemoContainer>
    <DemoContainer title="popover">
      <PopoverDemo />
    </DemoContainer>
    <DemoContainer title="progress">
      <ProgressDemo />
    </DemoContainer>
    <DemoContainer title="radio group">
      <RadioGroupDemo />
    </DemoContainer>
    <DemoContainer title="range calendar">
      <RangeCalendarDemo />
    </DemoContainer>
    <DemoContainer title="scroll area">
      <ScrollAreaDemo />
    </DemoContainer>
    <DemoContainer title="select">
      <SelectDemo />
    </DemoContainer>
    <DemoContainer title="separator">
      <SeparatorDemo />
    </DemoContainer>
    <DemoContainer title="slider">
      <SliderDemo />
    </DemoContainer>
    <DemoContainer title="splitter">
      <SplitterDemo />
    </DemoContainer>
    <DemoContainer title="stepper">
      <StepperDemo />
    </DemoContainer>
    <DemoContainer title="switch">
      <SwitchDemo />
    </DemoContainer>
    <DemoContainer title="tabs">
      <TabsDemo />
    </DemoContainer>
    <DemoContainer title="tags input">
      <TagsInputDemo />
    </DemoContainer>
    <DemoContainer title="time field">
      <TimeFieldDemo />
    </DemoContainer>
    <DemoContainer title="toast">
      <ToastDemo />
    </DemoContainer>
    <DemoContainer title="toggle">
      <ToggleDemo />
    </DemoContainer>
    <DemoContainer title="toggle group">
      <ToggleGroupDemo />
    </DemoContainer>
    <DemoContainer
      title="toolbar"
      overflow
    >
      <ToolbarDemo />
    </DemoContainer>
    <DemoContainer title="tooltip">
      <TooltipDemo />
    </DemoContainer>
    <DemoContainer title="tree">
      <TreeDemo />
    </DemoContainer>
  </div>
</template>
