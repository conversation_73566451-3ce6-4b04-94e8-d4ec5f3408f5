@import '@radix-ui/colors/black-alpha.css';
@import '@radix-ui/colors/grass.css';

.Icon {
  width: 1.5rem;
  height: 1.5rem;
}

.Calendar {
  margin-top: 1.5rem;
  border-width: 1px;
  border-color: #000000;
  background-color: #ffffff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 22px;
}

.CalendarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.CalendarNavButton {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  color: #000000;
  background-color: transparent;
  cursor: pointer;
}

.CalendarNavButton:hover {
  color: #ffffff;
  background-color: #000000;
}

.CalendarHeading {
  font-weight: 500;
  color: #000000;
  color: 15px;
}

.CalendarWrapper {
  display: flex;
  padding-top: 1rem;
  margin-top: 1rem;
  flex-direction: column;
}

@media (min-width: 640px) {
  .CalendarWrapper {
    margin-left: 1rem;
    margin-top: 0;
    flex-direction: row;
  }
}

.CalendarGrid {
  margin-top: 0.25rem;
  width: 100%;
  user-select: none;
  border-collapse: collapse;
}

.CalendarGridRow {
  display: grid;
  margin-bottom: 0.25rem;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  width: 100%;
}

.CalendarGridRow[data-reka-calendar-month-view] {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.CalendarHeadCell {
  border-radius: 0.375rem;
  font-size: 0.75rem;
  line-height: 1rem;
  color: #000000;
  font-weight: 400;
}

.CalendarCell {
  position: relative;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: center;
}

.CalendarCellTrigger {
  display: flex;
  position: relative;
  padding: 0.5rem;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  border-color: transparent;
  outline-style: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  color: #000000;
  white-space: nowrap;
  background-color: transparent;
}

.CalendarCellTrigger:hover {
  border-color: #000000;
}

.CalendarCellTrigger:focus {
  box-shadow: 0 0 0 2px #000000;
}

.CalendarCellTrigger[data-disabled] {
  cursor: none;
  color: rgba(0,0,0,0.3);
}

.CalendarCellTrigger[data-selected] {
  background-color: #000000;
  color: #ffffff;
  font-weight: 500;
}

.CalendarCellTrigger[data-selected]::before {
  background-color: #FFFFFF;
}

.CalendarCellTrigger[data-unavailable] {
  color: rgba(0,0,0,0.3);
  text-decoration: line-through;
}

.CalendarCellTrigger::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 0;
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 9999px;
  background-color: #FFFFFF;
}

.CalendarCellTrigger[data-today]::before {
  display: block;
  background-color: var(--grass-9);
}

/* reset */
button {
  all: unset;
}

.Wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.Label {
  color: #fff;
}

.SelectTrigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 13px;
  line-height: 1;
  height: 35px;
  gap: 5px;
  background-color: white;
  color: var(--grass-11);
  box-shadow: 0 2px 10px var(--black-a7);
}
.SelectTrigger:hover {
  background-color: var(--mauve-3);
}
.SelectTrigger:focus {
  box-shadow: 0 0 0 2px black;
}
.SelectTrigger[data-placeholder] {
  color: var(--grass-9);
}

.SelectIcon {
  color: Var(--grass-11);
}

.SelectContent {
  overflow: hidden;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0px 10px 38px -10px rgba(22, 23, 24, 0.35), 0px 10px 20px -15px rgba(22, 23, 24, 0.2);
}

.SelectViewport {
  padding: 5px;
}

.SelectItem {
  font-size: 13px;
  line-height: 1;
  color: var(--grass-11);
  border-radius: 3px;
  display: flex;
  align-items: center;
  height: 25px;
  padding: 0 35px 0 25px;
  position: relative;
  user-select: none;
}
.SelectItem[data-disabled] {
  color: var(--mauve-8);
  pointer-events: none;
}
.SelectItem[data-highlighted] {
  outline: none;
  background-color: var(--grass-9);
  color: var(--grass-1);
}

.SelectLabel {
  padding: 0 25px;
  font-size: 12px;
  line-height: 25px;
  color: var(--mauve-11);
}

.SelectSeparator {
  height: 1px;
  background-color: var(--grass-6);
  margin: 5px;
}

.SelectItemIndicator {
  position: absolute;
  left: 0;
  width: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.SelectScrollButton {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
  background-color: white;
  color: var(--grass-11);
  cursor: default;
}
