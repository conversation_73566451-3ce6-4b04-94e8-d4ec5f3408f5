@import '@radix-ui/colors/black-alpha.css';
@import '@radix-ui/colors/grass.css';

/* reset */
button {
  all: unset;
}

.CollapsibleRoot {
  width: 300px;
}

.IconButton {
  font-family: inherit;
  border-radius: 100%;
  height: 25px;
  width: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--grass-11);
  box-shadow: 0 2px 10px var(--black-a7);
}
.IconButton[data-state='closed'] {
  background-color: white;
}
.IconButton[data-state='open'] {
  background-color: var(--grass-3);
}
.IconButton:hover {
  background-color: var(--grass-3);
}
.IconButton:focus {
  box-shadow: 0 0 0 2px black;
}

.Text {
  color: var(--grass-11);
  font-size: 15px;
  line-height: 25px;
}

.Repository {
  background-color: white;
  border-radius: 4px;
  margin: 10px 0;
  padding: 10px;
  box-shadow: 0 2px 10px var(--black-a7);
}