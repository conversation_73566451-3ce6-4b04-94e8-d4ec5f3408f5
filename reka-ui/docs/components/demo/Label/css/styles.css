@import '@radix-ui/colors/black-alpha.css';

/* reset */
input {
  all: unset;
}

.LabelRoot {
  font-size: 15px;
  font-weight: 500;
  line-height: 35px;
  color: white;
}

.Input {
  width: 200px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0 10px;
  height: 35px;
  font-size: 15px;
  line-height: 1;
  color: white;
  background-color: var(--black-a5);
  box-shadow: 0 0 0 1px var(--black-a9);
}
.Input:focus {
  box-shadow: 0 0 0 2px black;
}
.Input::selection {
  background-color: var(--black-a9);
  color: white;
}