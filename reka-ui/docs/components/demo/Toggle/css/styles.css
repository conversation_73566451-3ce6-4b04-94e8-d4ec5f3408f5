@import '@radix-ui/colors/black-alpha.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/grass.css';

/* reset */
button {
  all: unset;
}

.Toggle {
  background-color: white;
  color: var(--mauve-11);
  height: 35px;
  width: 35px;
  border-radius: 4px;
  display: flex;
  font-size: 15px;
  line-height: 1;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px var(--black-a7);
}
.Toggle:hover {
  background-color: var(--grass-3);
}
.Toggle[data-state='on'] {
  background-color: var(--grass-6);
  color: var(--grass-12);
}
.Toggle:focus {
  box-shadow: 0 0 0 2px black;
}