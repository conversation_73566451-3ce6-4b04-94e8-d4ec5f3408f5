<script setup lang="ts">
import { I<PERSON> } from '@iconify/vue'
import { ComboboxAnchor, ComboboxContent, ComboboxGroup, ComboboxInput, ComboboxItem, ComboboxItemIndicator, ComboboxLabel, ComboboxRoot, ComboboxSeparator, ComboboxTrigger, ComboboxViewport, useFilter } from 'reka-ui'
import { computed, ref } from 'vue'

const { contains } = useFilter({ sensitivity: 'base' })

const v = ref('')
const query = ref('')

const options = [
  { name: '<PERSON>', children: [
    { name: '<PERSON>' },
    { name: '<PERSON><PERSON>' },
    { name: '<PERSON>' },
    { name: '<PERSON>de<PERSON>' },
    { name: '<PERSON>rap<PERSON>' },
    { name: '<PERSON><PERSON><PERSON>' },
    { name: '<PERSON><PERSON><PERSON><PERSON>' },
    { name: '<PERSON>ear' },
  ] },
  { name: 'Vegetable', children: [
    { name: 'Cabbage' },
    { name: '<PERSON><PERSON><PERSON><PERSON>' },
    { name: '<PERSON><PERSON>' },
    { name: 'Lettu<PERSON>' },
    { name: '<PERSON><PERSON>' },
    { name: '<PERSON><PERSON><PERSON>' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { name: '<PERSON><PERSON><PERSON>' },
  ] },
]

const filteredOptions = computed(() =>
  options
    .map(group => ({ name: group.name, children: group.children.filter(option => contains(option.name, query.value)) }))
    .filter(group => group.children.length),
)
</script>

<template>
  <ComboboxRoot class="relative">
    <ComboboxAnchor class="min-w-[160px] inline-flex items-center justify-between rounded px-[15px] text-[13px] leading-none h-[35px] gap-[5px] bg-white text-grass11 shadow-[0_2px_10px] shadow-black/10 hover:bg-mauve3 focus:shadow-[0_0_0_2px] focus:shadow-black data-[placeholder]:text-grass9 outline-none">
      <ComboboxInput
        v-model="query"
        class="!bg-transparent outline-none text-grass11 h-full selection:bg-grass5 placeholder-mauve8"
        placeholder="Placeholder..."
      />
      <ComboboxTrigger>
        <Icon
          icon="radix-icons:chevron-down"
          class="h-4 w-4 text-grass11"
        />
      </ComboboxTrigger>
    </ComboboxAnchor>

    <ComboboxContent class="absolute z-10 w-full mt-2 min-w-[160px] bg-white overflow-hidden rounded shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),_0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)] will-change-[opacity,transform] data-[side=top]:animate-slideDownAndFade data-[side=right]:animate-slideLeftAndFade data-[side=bottom]:animate-slideUpAndFade data-[side=left]:animate-slideRightAndFade">
      <ComboboxViewport class="p-[5px]">
        <template
          v-for="(group, index) in filteredOptions"
          :key="group.name"
        >
          <ComboboxGroup v-if="group.children.length">
            <ComboboxSeparator
              v-if="index !== 0"
              class="h-[1px] bg-grass6 m-[5px]"
            />

            <ComboboxLabel class="px-[25px] text-xs leading-[25px] text-mauve11">
              {{ group.name }}
            </ComboboxLabel>

            <ComboboxItem
              v-for="option in group.children"
              :key="option.name"
              :value="option.name"
              class="text-[13px] leading-none text-grass11 rounded-[3px] flex items-center h-[25px] pr-[35px] pl-[25px] relative select-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:outline-none data-[highlighted]:bg-grass9 data-[highlighted]:text-grass1"
            >
              <ComboboxItemIndicator
                class="absolute left-0 w-[25px] inline-flex items-center justify-center"
              >
                <Icon icon="radix-icons:check" />
              </ComboboxItemIndicator>
              <span>
                {{ option.name }}
              </span>
            </ComboboxItem>
          </ComboboxGroup>
        </template>
      </ComboboxViewport>
    </ComboboxContent>
  </ComboboxRoot>
</template>
