---
title: Introduction
description: An open-source UI component library for building high-quality, accessible design systems and web apps using Vue.
---

<script setup>
import Contributors from '../../../.vitepress/components/Contributors.vue'
</script>

# Introduction

<Description>

An open-source UI component library for building high-quality, accessible
design systems and web apps using <a href="https://vuejs.org" target="_blank" rel="noopener noreferrer">Vue.js</a>.

</Description>

## ✨ Rebrand: Reka UI ✨

Presenting **Reka UI**, the new identity of [Radix Vue](https://www.radix-vue.com/) in its v2 evolution.

**Reka** (pronounced `/ree·kuh/`) means "design" in [Malay](https://translate.google.com/?hl=en&sl=ms&tl=en&text=reka&op=translate), and also evokes "Eureka."

Reka UI strives to deliver a low-level UI component library centered on accessibility, customization, and developer experience. Use these components as the foundation of your design system or integrate them progressively.

Check out the release note [here](/docs/overview/releases#_2-0-changes)

<Callout type="tip">

Curious about the rebrand? See the announcement in [this discussion](https://github.com/unovue/reka-ui/issues/908).

</Callout>

## Our Principles

### Accessibility-First

Accessibility is at the heart of Reka UI. Our components align with [WAI-ARIA design patterns](https://www.w3.org/TR/wai-aria-practices-1.2) to ensure that all users, regardless of abilities, can interact with your UI effectively. We handle intricate accessibility details like aria attributes, keyboard navigation, and focus management to simplify the developer's work.

### Customizable & Unstyled

Reka UI components come unstyled, providing developers the freedom to style them however they choose, using any CSS solution (vanilla CSS, preprocessors, or CSS-in-JS libraries). Our open component architecture allows you to wrap, extend, or modify each component as needed. Explore more in our [styling guide](../guides/styling).

### Open & Modular

Our components are designed to be open and adaptable, allowing you to customize each element to fit your needs. Whether adding event listeners, props, or refs, Reka UI provides granular access to each component's inner workings.

### Flexible State Management

Reka UI components are, by default, uncontrolled but can also be fully controlled when needed. This approach allows developers to decide on the level of state management required, offering a balance between flexibility and ease of use.

### Developer-Centric Experience

We prioritize developer experience by maintaining a consistent and predictable API. Reka UI is fully-typed and structured with simplicity in mind, ensuring that components are easy to use and integrate. Our `asChild` prop allows full control over rendered elements, enhancing flexibility.

### Performance & Tree-Shaking

Our library is designed with performance in mind. All components are compiled into a single package, making installation straightforward and ensuring that any unused components won’t add to your bundle size thanks to tree-shaking.

<Callout type="tip">

Reka UI is inspired by the principles and goals of [Radix UI](https://www.radix-ui.com/), sharing a commitment to accessibility, customization, and developer-friendly design.

</Callout>

---

# Built by Vue lovers 💚

<Contributors />

# Credits

All credits go to these open-source works and resources

- Radix UI - https://radix-ui.com
- React Aria - https://react-spectrum.adobe.com/react-aria
- Floating UI - https://floating-ui.com
- VueUse - https://vueuse.org
- HeadlessUI - https://headlessui.com
- Ariakit - https://ariakit.org/
