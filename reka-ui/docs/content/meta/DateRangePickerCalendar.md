<!-- This file was automatic generated. Do not edit it manually -->

<SlotsTable :data="[
  {
    'name': 'date',
    'description': '',
    'type': 'DateValue'
  },
  {
    'name': 'grid',
    'description': '',
    'type': 'Grid<DateValue>[]'
  },
  {
    'name': 'weekDays',
    'description': '',
    'type': 'string[]'
  },
  {
    'name': 'weekStartsOn',
    'description': '',
    'type': '0 | 1 | 2 | 3 | 4 | 5 | 6'
  },
  {
    'name': 'locale',
    'description': '',
    'type': 'string'
  },
  {
    'name': 'fixedWeeks',
    'description': '',
    'type': 'boolean'
  }
]" />
