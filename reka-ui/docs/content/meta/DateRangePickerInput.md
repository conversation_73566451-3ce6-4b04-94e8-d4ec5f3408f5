<!-- This file was automatic generated. Do not edit it manually -->

<PropsTable :data="[
  {
    'name': 'as',
    'description': '<p>The element or component this component should render as. Can be overwritten by <code>asChild</code>.</p>\n',
    'type': 'AsTag | Component',
    'required': false,
    'default': '\'div\''
  },
  {
    'name': 'asChild',
    'description': '<p>Change the default rendered element for the one passed as a child, merging their props and behavior.</p>\n<p>Read our <a href=\'https://www.reka-ui.com/docs/guides/composition\'>Composition</a> guide for more details.</p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'part',
    'description': '<p>The part of the date to render</p>\n',
    'type': '\'day\' | \'month\' | \'year\' | \'hour\' | \'minute\' | \'second\' | \'dayPeriod\' | \'literal\' | \'timeZoneName\'',
    'required': true
  },
  {
    'name': 'type',
    'description': '<p>The type of field to render (start or end)</p>\n',
    'type': '\'start\' | \'end\'',
    'required': true
  }
]" />
