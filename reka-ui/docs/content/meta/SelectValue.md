<!-- This file was automatic generated. Do not edit it manually -->

<PropsTable :data="[
  {
    'name': 'as',
    'description': '<p>The element or component this component should render as. Can be overwritten by <code>asChild</code>.</p>\n',
    'type': 'AsTag | Component',
    'required': false,
    'default': '\'span\''
  },
  {
    'name': 'asChild',
    'description': '<p>Change the default rendered element for the one passed as a child, merging their props and behavior.</p>\n<p>Read our <a href=\'https://www.reka-ui.com/docs/guides/composition\'>Composition</a> guide for more details.</p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'placeholder',
    'description': '<p>The content that will be rendered inside the <code>SelectValue</code> when no <code>value</code> or <code>defaultValue</code> is set.</p>\n',
    'type': 'string',
    'required': false,
    'default': '\'\''
  }
]" />

<SlotsTable :data="[
  {
    'name': 'selectedLabel',
    'description': '',
    'type': 'string[]'
  },
  {
    'name': 'modelValue',
    'description': '',
    'type': 'AcceptableValue | AcceptableValue[] | undefined'
  }
]" />
